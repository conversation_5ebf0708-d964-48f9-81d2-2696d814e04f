# train_eegsym.py
# EEGSym-based training script adapted from train15 copy.py
import os
import sys
import argparse
import numpy as np
import tensorflow as tf
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.callbacks import EarlyStopping
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score,
    precision_recall_fscore_support,
    roc_auc_score,
)
from tqdm import tqdm
from EEGSym_architecture import EEGSym
from EEGSym_DataAugmentation import trial_iterator
from config_eegsym import get_config

# GPU配置
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"Found {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(e)
else:
    print("No GPU found, using CPU")

def load_all_subjects_data(data_dir, num_subjects=15):
    """加载所有被试的数据"""
    X_list, y_list = [], []
    for sid in range(1, num_subjects+1):
        X = np.load(os.path.join(data_dir, f"subject_{sid}_X.npy"))  # (Ni,9,62,5)
        y = np.load(os.path.join(data_dir, f"subject_{sid}_Y.npy"))  # (Ni,)
        y = np.where(y == -1, 2, y)  # 将-1标签转换为2
        X_list.append(X)
        y_list.append(y)
        print(f"Subject {sid:2d}: X={X.shape}, y={y.shape}")
    
    X_all = np.concatenate(X_list, axis=0)
    y_all = np.concatenate(y_list, axis=0)
    print(f"Loaded total {X_all.shape[0]} samples")
    return X_all, y_all

def reshape_for_eegsym(X, target_fs=250):
    """
    将数据重塑为EEGSym所需的格式
    输入: (N, 9, 62, 5) - (样本数, 频带, 电极, 时间窗)
    输出: (N, 时间点, 电极, 1) - EEGSym期望的格式

    简化方法：将频带和时间窗展开为时间序列，然后插值到目标长度
    """
    from scipy import interpolate

    N, freq_bands, channels, time_windows = X.shape
    print(f"Input shape: {X.shape}")

    # 目标时间点数（3秒 * 采样率）
    target_samples = int(3 * target_fs)  # 3秒 * 250Hz = 750个样本点

    # 初始化输出数组
    X_reshaped = np.zeros((N, target_samples, channels, 1))

    for i in range(N):
        # 取出单个样本 (9, 62, 5)
        sample = X[i]  # (9, 62, 5)

        # 对每个电极处理
        for ch in range(channels):
            # 取出该电极的所有频带和时间窗数据 (9, 5)
            ch_data = sample[:, ch, :]  # (9, 5)

            # 展平为一维时间序列 (45,)
            ch_flat = ch_data.flatten()  # (45,)

            # 插值到目标长度
            current_length = len(ch_flat)
            if current_length > 1:
                x_old = np.linspace(0, 1, current_length)
                x_new = np.linspace(0, 1, target_samples)
                f = interpolate.interp1d(x_old, ch_flat, kind='linear')
                ch_interp = f(x_new)
            else:
                # 如果只有一个点，则复制到所有时间点
                ch_interp = np.full(target_samples, ch_flat[0])

            # 存储到输出数组
            X_reshaped[i, :, ch, 0] = ch_interp

    print(f"Output shape: {X_reshaped.shape}")
    return X_reshaped

def create_eegsym_model(config, ncha, n_classes):
    """创建EEGSym模型"""

    # 从配置获取超参数
    hyperparameters = config.get_eegsym_hyperparameters(ncha, n_classes)

    print(f"Creating EEGSym model with parameters:")
    for key, value in hyperparameters.items():
        print(f"  {key}: {value}")

    model = EEGSym(**hyperparameters)

    # 加载预训练权重（如果可用且电极数匹配）
    if config.USE_PRETRAINED and ncha in [8, 16]:
        try:
            pretrained_path = f'EEGSym_pretrained_weights_{ncha}_electrode.h5'
            if os.path.exists(pretrained_path):
                model.load_weights(pretrained_path)
                print(f"Loaded pretrained weights from {pretrained_path}")

                # 冻结除最后一层外的所有层（用于微调）
                for layer in model.layers[:-1]:
                    layer.trainable = False
                print("Frozen all layers except the last one for fine-tuning")
            else:
                print(f"Pretrained weights file {pretrained_path} not found")
        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")

    return model

def train_model(model, config, X_train, y_train, X_val, y_val):
    """训练EEGSym模型"""

    # 转换标签为one-hot编码
    n_classes = len(np.unique(np.concatenate([y_train, y_val])))
    y_train_cat = to_categorical(y_train, num_classes=n_classes)
    y_val_cat = to_categorical(y_val, num_classes=n_classes)

    # 设置早停回调
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=config.EARLY_STOPPING_PATIENCE,
        restore_best_weights=True,
        verbose=config.VERBOSE
    )

    callbacks = [early_stopping]

    # 训练模型
    if config.USE_AUGMENTATION:
        # 使用数据增强
        print("Training with data augmentation...")
        steps_per_epoch = len(X_train) // config.BATCH_SIZE

        history = model.fit(
            trial_iterator(X_train, y_train_cat,
                         batch_size=config.BATCH_SIZE,
                         shuffle=True,
                         augmentation=True),
            steps_per_epoch=steps_per_epoch,
            epochs=config.EPOCHS,
            validation_data=(X_val, y_val_cat),
            callbacks=callbacks,
            verbose=config.VERBOSE
        )
    else:
        # 不使用数据增强
        print("Training without data augmentation...")
        history = model.fit(
            X_train, y_train_cat,
            batch_size=config.BATCH_SIZE,
            epochs=config.EPOCHS,
            validation_data=(X_val, y_val_cat),
            callbacks=callbacks,
            verbose=config.VERBOSE,
            shuffle=True
        )

    return history

def evaluate_model(model, config, X_test, y_test):
    """评估模型性能"""

    # 预测
    y_pred_proba = model.predict(X_test, batch_size=config.BATCH_SIZE)
    y_pred = np.argmax(y_pred_proba, axis=1)

    # 计算各种指标
    accuracy = accuracy_score(y_test, y_pred)
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_test, y_pred, average='macro', zero_division=0
    )

    # 计算AUC（多分类）
    n_classes = len(np.unique(y_test))
    y_test_cat = to_categorical(y_test, num_classes=n_classes)
    try:
        auc = roc_auc_score(y_test_cat, y_pred_proba, multi_class='ovr', average='macro')
    except ValueError:
        auc = 0.0  # 如果计算AUC失败，设为0

    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc,
        'y_pred': y_pred,
        'y_pred_proba': y_pred_proba
    }

def print_results(metrics_list):
    """打印最终结果统计"""
    print("\nFinal Results (mean ± std):")
    print("-" * 50)

    for metric_name in ['accuracy', 'precision', 'recall', 'f1', 'auc']:
        values = [m[metric_name] for m in metrics_list]
        mean_val = np.mean(values)
        std_val = np.std(values)
        print(f"{metric_name.upper():>10}: {mean_val:.4f} ± {std_val:.4f}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='EEGSym Training Script')
    parser.add_argument('--config', type=str, default='default',
                       choices=['default', 'quick_test', 'full_training',
                               'pretrained_finetune', 'no_augmentation'],
                       help='Configuration to use')
    parser.add_argument('--data_dir', type=str, default=None,
                       help='Data directory path (overrides config)')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Number of epochs (overrides config)')
    parser.add_argument('--batch_size', type=int, default=None,
                       help='Batch size (overrides config)')
    parser.add_argument('--lr', type=float, default=None,
                       help='Learning rate (overrides config)')
    parser.add_argument('--num_runs', type=int, default=None,
                       help='Number of runs (overrides config)')
    parser.add_argument('--no_pretrained', action='store_true',
                       help='Disable pretrained weights')
    parser.add_argument('--no_augmentation', action='store_true',
                       help='Disable data augmentation')

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 获取配置
    config = get_config(args.config)

    # 根据命令行参数覆盖配置
    if args.data_dir:
        config.DATA_DIR = args.data_dir
    if args.epochs:
        config.EPOCHS = args.epochs
    if args.batch_size:
        config.BATCH_SIZE = args.batch_size
    if args.lr:
        config.LR = args.lr
    if args.num_runs:
        config.NUM_RUNS = args.num_runs
    if args.no_pretrained:
        config.USE_PRETRAINED = False
    if args.no_augmentation:
        config.USE_AUGMENTATION = False

    # 打印配置信息
    config.print_config()

    # 设置随机种子
    np.random.seed(config.RANDOM_SEED)
    tf.random.set_seed(config.RANDOM_SEED)

    # 1. 加载数据
    print("\n1. Loading data...")
    X_all, y_all = load_all_subjects_data(config.DATA_DIR)
    print(f"Original data shape: {X_all.shape}")
    print(f"Labels shape: {y_all.shape}")
    print(f"Unique labels: {np.unique(y_all)}")

    # 2. 数据预处理
    print("\n2. Preprocessing data for EEGSym...")
    X_all_eegsym = reshape_for_eegsym(X_all, target_fs=config.TARGET_FS)
    print(f"Reshaped data shape: {X_all_eegsym.shape}")

    # 获取数据维度信息
    n_samples, n_timepoints, n_channels, _ = X_all_eegsym.shape
    n_classes = len(np.unique(y_all))

    print(f"Number of samples: {n_samples}")
    print(f"Number of timepoints: {n_timepoints}")
    print(f"Number of channels: {n_channels}")
    print(f"Number of classes: {n_classes}")

    # 存储所有运行的结果
    all_metrics = []

    # 3. 多次运行实验
    for run in range(config.NUM_RUNS):
        print(f"\n{'='*20} Run {run+1}/{config.NUM_RUNS} {'='*20}")

        # 设置当前运行的随机种子
        current_seed = config.RANDOM_SEED + run
        np.random.seed(current_seed)
        tf.random.set_seed(current_seed)

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_all_eegsym, y_all,
            test_size=config.TEST_SIZE,
            stratify=y_all,
            random_state=current_seed
        )

        # 进一步分割训练集为训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train,
            test_size=config.VAL_SIZE,
            stratify=y_train,
            random_state=current_seed
        )

        print(f"Train set: {X_train.shape}, {y_train.shape}")
        print(f"Validation set: {X_val.shape}, {y_val.shape}")
        print(f"Test set: {X_test.shape}, {y_test.shape}")

        # 4. 创建模型
        print("\n4. Creating EEGSym model...")
        model = create_eegsym_model(config, n_channels, n_classes)

        if config.VERBOSE:
            print(f"\nModel summary:")
            model.summary()

        # 5. 训练模型
        print(f"\n5. Training model...")
        history = train_model(model, config, X_train, y_train, X_val, y_val)

        # 6. 评估模型
        print(f"\n6. Evaluating model...")
        metrics = evaluate_model(model, config, X_test, y_test)
        all_metrics.append(metrics)

        # 打印当前运行结果
        print(f"\nRun {run+1} Results:")
        print(f"  Accuracy:  {metrics['accuracy']:.4f}")
        print(f"  Precision: {metrics['precision']:.4f}")
        print(f"  Recall:    {metrics['recall']:.4f}")
        print(f"  F1-Score:  {metrics['f1']:.4f}")
        print(f"  AUC:       {metrics['auc']:.4f}")

        # 可选：保存模型
        if config.SAVE_MODEL and config.NUM_RUNS == 1:  # 只在单次运行时保存模型
            model_save_path = f"eegsym_model_{config.config_name}_run_{run+1}.h5"
            model.save_weights(model_save_path)
            print(f"Model weights saved to: {model_save_path}")

    # 7. 打印最终统计结果
    print_results(all_metrics)

    print(f"\nTraining completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()
