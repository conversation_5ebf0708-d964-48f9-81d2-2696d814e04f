# run_gpu_training.py
# GPU训练启动脚本

import sys
import subprocess
import argparse
from gpu_monitor import check_gpu_requirements, estimate_memory_requirements, GPUMonitor

def check_environment():
    """检查训练环境"""
    print("="*60)
    print("EEGSym GPU Training Environment Check")
    print("="*60)
    
    # 检查GPU要求
    if not check_gpu_requirements():
        print("\n❌ GPU requirements not met!")
        return False
    
    # 估算内存需求
    print("\n" + "-"*40)
    estimate_memory_requirements(batch_size=16, n_samples=750, n_channels=62)
    
    print("\n✅ Environment check passed!")
    return True

def run_training_with_monitoring(config_name, additional_args=""):
    """运行训练并监控GPU使用"""
    
    # 构建训练命令
    cmd = f"python train_eegsym.py --config {config_name}"
    if additional_args:
        cmd += f" {additional_args}"
    
    print(f"\n{'='*60}")
    print(f"Starting GPU Training")
    print(f"{'='*60}")
    print(f"Command: {cmd}")
    print(f"Config: {config_name}")
    
    # 启动GPU监控
    monitor = GPUMonitor(interval=10)  # 每10秒监控一次
    
    try:
        # 开始监控
        monitor.start_monitoring()
        
        # 运行训练
        print(f"\n🚀 Starting training...")
        subprocess.run(cmd, shell=True, check=True)
        
        print(f"\n✅ Training completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with return code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠ Training interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        return False
    finally:
        # 停止监控
        monitor.stop_monitoring()

def main():
    parser = argparse.ArgumentParser(description='EEGSym GPU Training Launcher')
    parser.add_argument('--config', type=str, default='default',
                       choices=['default', 'quick_test', 'full_training', 
                               'pretrained_finetune', 'no_augmentation'],
                       help='Training configuration')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check environment, do not train')
    parser.add_argument('--no-monitor', action='store_true',
                       help='Disable GPU monitoring during training')
    parser.add_argument('--batch-size', type=int, default=None,
                       help='Override batch size')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Override number of epochs')
    parser.add_argument('--lr', type=float, default=None,
                       help='Override learning rate')
    parser.add_argument('--data-dir', type=str, default=None,
                       help='Override data directory')
    
    args = parser.parse_args()
    
    # 检查环境
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        return False
    
    if args.check_only:
        print("\n✅ Environment check completed. Ready for training!")
        return True
    
    # 构建额外参数
    additional_args = []
    if args.batch_size:
        additional_args.append(f"--batch_size {args.batch_size}")
    if args.epochs:
        additional_args.append(f"--epochs {args.epochs}")
    if args.lr:
        additional_args.append(f"--lr {args.lr}")
    if args.data_dir:
        additional_args.append(f"--data_dir {args.data_dir}")
    
    additional_args_str = " ".join(additional_args)
    
    # 根据配置给出建议
    config_suggestions = {
        'quick_test': {
            'description': 'Quick test for debugging (5 epochs, small batch)',
            'estimated_time': '5-10 minutes',
            'memory_usage': 'Low (~2-4GB)'
        },
        'default': {
            'description': 'Standard training (50 epochs)',
            'estimated_time': '1-2 hours',
            'memory_usage': 'Medium (~4-8GB)'
        },
        'pretrained_finetune': {
            'description': 'Fine-tuning with pretrained weights',
            'estimated_time': '30-60 minutes',
            'memory_usage': 'Medium (~4-8GB)'
        },
        'full_training': {
            'description': 'Complete training with multiple runs',
            'estimated_time': '3-6 hours',
            'memory_usage': 'High (~6-12GB)'
        },
        'no_augmentation': {
            'description': 'Training without data augmentation',
            'estimated_time': '1-3 hours',
            'memory_usage': 'Medium (~4-8GB)'
        }
    }
    
    if args.config in config_suggestions:
        suggestion = config_suggestions[args.config]
        print(f"\n📋 Configuration: {args.config}")
        print(f"   Description: {suggestion['description']}")
        print(f"   Estimated time: {suggestion['estimated_time']}")
        print(f"   Memory usage: {suggestion['memory_usage']}")
    
    # 确认开始训练
    response = input(f"\n🤔 Start training with config '{args.config}'? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Training cancelled.")
        return False
    
    # 运行训练
    if args.no_monitor:
        # 不使用监控，直接运行
        cmd = f"python train_eegsym.py --config {args.config}"
        if additional_args_str:
            cmd += f" {additional_args_str}"
        
        print(f"\n🚀 Starting training without monitoring...")
        try:
            subprocess.run(cmd, shell=True, check=True)
            print(f"\n✅ Training completed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"\n❌ Training failed with return code {e.returncode}")
            return False
    else:
        # 使用监控运行
        return run_training_with_monitoring(args.config, additional_args_str)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
