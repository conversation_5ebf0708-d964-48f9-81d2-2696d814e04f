<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>EEGSym.EEGSym_DataAugmentation API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>EEGSym.EEGSym_DataAugmentation</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">import tensorflow as tf
import numpy as np


def preprocessing_function(augmentation=True):
    &#34;&#34;&#34;Custom Data Augmentation for EEGSym.

        Parameters
        ----------
        augmentation : Bool
            If the augmentation is performed to the input.

        Returns
        -------
        data_augmentation : function
            Data augmentation performed to each trial
    &#34;&#34;&#34;

    def data_augmentation(trial):
        &#34;&#34;&#34;Custom Data Augmentation for EEGSym.

            Parameters
            ----------
            trial : tf.tensor
                Input of the

            Returns
            -------
            data_augmentation : keras.models.Model
                Data augmentation performed to each trial
        &#34;&#34;&#34;

        samples, ncha, _ = trial.shape

        augmentations = dict()
        augmentations[&#34;patch_perturbation&#34;] = 0
        augmentations[&#34;random_shift&#34;] = 0
        augmentations[&#34;hemisphere_perturbation&#34;] = 0
        augmentations[&#34;no_augmentation&#34;] = 0

        selectionables = [&#34;patch_perturbation&#34;, &#34;random_shift&#34;,
                          &#34;hemisphere_perturbation&#34;, &#34;no_augmentation&#34;]
        probabilities = None

        if augmentation:
            selection = np.random.choice(selectionables, p=probabilities)
            augmentations[selection] = 1

            method = np.random.choice((0, 2))
            std = &#39;self&#39;
            # elif data_augmentation == 1:  # Random shift
            for _ in range(augmentations[&#34;random_shift&#34;]):  # Random shift
                # Select position where to erase that timeframe
                position = 0
                if position == 0:
                    samples_shifted = np.random.randint(low=1, high=int(
                        samples * 0.5 / 3))
                else:
                    samples_shifted = np.random.randint(low=1, high=int(
                        samples * 0.1 / 3))

                if method == 0:
                    shifted_samples = np.zeros((samples_shifted, ncha, 1))
                else:
                    if std == &#39;self&#39;:
                        std_applied = np.std(trial)
                    else:
                        std_applied = std
                    center = 0
                    shifted_samples = np.random.normal(center, std_applied,
                                                       (samples_shifted, ncha,
                                                        1))
                if position == 0:
                    trial = np.concatenate((shifted_samples, trial),
                                           axis=0)[:samples]
                else:
                    trial = np.concatenate((trial, shifted_samples),
                                           axis=0)[samples_shifted:]

            for _ in range(
                    augmentations[&#34;patch_perturbation&#34;]):  # Patch perturbation
                channels_affected = np.random.randint(low=1, high=ncha - 1)
                pct_max = 1
                pct_min = 0.2
                pct_erased = np.random.uniform(low=pct_min, high=pct_max)
                # Select time to be erased acording to pct_erased
                # samples_erased = np.min((int(samples*ncha*pct_erased//channels_affected), samples))#np.random.randint(low=1, high=samples//3)
                samples_erased = int(samples * pct_erased)
                # Select position where to erase that timeframe
                if samples_erased != samples:
                    samples_idx = np.arange(samples_erased) + np.random.randint(
                        samples - samples_erased)
                else:
                    samples_idx = np.arange(samples_erased)
                # Select indexes to erase (always keep at least a channel)
                channel_idx = np.random.permutation(np.arange(ncha))[
                              :channels_affected]
                channel_idx.sort()
                for channel in channel_idx:
                    if method == 0:
                        trial[samples_idx, channel] = 0
                    else:
                        if std == &#39;self&#39;:
                            std_applied = np.std(trial[:, channel]) \
                                          * np.random.uniform(low=0.01, high=2)
                        else:
                            std_applied = std
                        center = 0
                        trial[samples_idx, channel] += \
                            np.random.normal(center, std_applied,
                                             trial[samples_idx, channel,
                                             :].shape)
                        # Standarize the channel again after the change
                        temp_trial_ch_mean = np.mean(trial[:, channel], axis=0)
                        temp_trial_ch_std = np.std(trial[:, channel], axis=0)
                        trial[:, channel] = (trial[:,
                                             channel] - temp_trial_ch_mean) / temp_trial_ch_std

            for _ in range(augmentations[&#34;hemisphere_perturbation&#34;]):
                # Select side to mix/change for noise
                left_right = np.random.choice((0, 1))
                if method == 0:
                    if left_right == 1:
                        channel_idx = np.arange(ncha)[:int((ncha / 2) - 1)]
                        channel_mix = np.random.permutation(channel_idx.copy())
                    else:
                        channel_idx = np.arange(ncha)[-int((ncha / 2) - 1):]
                        channel_mix = np.random.permutation(channel_idx.copy())
                    temp_trial = trial.copy()
                    for channel, channel_mixed in zip(channel_idx, channel_mix):
                        temp_trial[:, channel] = trial[:, channel_mixed]
                    trial = temp_trial
                else:
                    if left_right == 1:
                        channel_idx = np.arange(ncha)[:int((ncha / 2) - 1)]
                    else:
                        channel_idx = np.arange(ncha)[-int((ncha / 2) - 1):]
                    for channel in channel_idx:
                        trial[:, channel] = np.random.normal(0, 1,
                                                             trial[:,
                                                             channel].shape)

        return trial

    return data_augmentation


def trial_iterator(X, y, batch_size=32, shuffle=True, augmentation=True):
    &#34;&#34;&#34;Custom trial iterator to pretrain EEGSym.

        Parameters
        ----------
        X : tf.tensor
            Input tensor of  EEG features.
        y : tf.tensor
            Input tensor of  labels.
        batch_size : int
            Number of features in each batch.
        shuffle : Bool
            If the features are shuffled at each training epoch.
        augmentation : Bool
            If the augmentation is performed to the input.

        Returns
        -------
        trial_iterator : tf.keras.preprocessing.image.NumpyArrayIterator
            Iterator used to train the model.
    &#34;&#34;&#34;

    trial_data_generator = tf.keras.preprocessing.image.ImageDataGenerator(
        preprocessing_function=preprocessing_function(
            augmentation=augmentation))

    trial_iterator = tf.keras.preprocessing.image.NumpyArrayIterator(
        X, y, trial_data_generator, batch_size=batch_size, shuffle=shuffle,
        sample_weight=None,
        seed=None, data_format=None, save_to_dir=None, save_prefix=&#39;&#39;,
        save_format=&#39;png&#39;, subset=None, dtype=None
    )
    return trial_iterator</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-functions">Functions</h2>
<dl>
<dt id="EEGSym.EEGSym_DataAugmentation.preprocessing_function"><code class="name flex">
<span>def <span class="ident">preprocessing_function</span></span>(<span>augmentation=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Custom Data Augmentation for EEGSym.</p>
<h2 id="parameters">Parameters</h2>
<dl>
<dt><strong><code>augmentation</code></strong> :&ensp;<code>Bool</code></dt>
<dd>If the augmentation is performed to the input.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><strong><code>data_augmentation</code></strong> :&ensp;<code>function</code></dt>
<dd>Data augmentation performed to each trial</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def preprocessing_function(augmentation=True):
    &#34;&#34;&#34;Custom Data Augmentation for EEGSym.

        Parameters
        ----------
        augmentation : Bool
            If the augmentation is performed to the input.

        Returns
        -------
        data_augmentation : function
            Data augmentation performed to each trial
    &#34;&#34;&#34;

    def data_augmentation(trial):
        &#34;&#34;&#34;Custom Data Augmentation for EEGSym.

            Parameters
            ----------
            trial : tf.tensor
                Input of the

            Returns
            -------
            data_augmentation : keras.models.Model
                Data augmentation performed to each trial
        &#34;&#34;&#34;

        samples, ncha, _ = trial.shape

        augmentations = dict()
        augmentations[&#34;patch_perturbation&#34;] = 0
        augmentations[&#34;random_shift&#34;] = 0
        augmentations[&#34;hemisphere_perturbation&#34;] = 0
        augmentations[&#34;no_augmentation&#34;] = 0

        selectionables = [&#34;patch_perturbation&#34;, &#34;random_shift&#34;,
                          &#34;hemisphere_perturbation&#34;, &#34;no_augmentation&#34;]
        probabilities = None

        if augmentation:
            selection = np.random.choice(selectionables, p=probabilities)
            augmentations[selection] = 1

            method = np.random.choice((0, 2))
            std = &#39;self&#39;
            # elif data_augmentation == 1:  # Random shift
            for _ in range(augmentations[&#34;random_shift&#34;]):  # Random shift
                # Select position where to erase that timeframe
                position = 0
                if position == 0:
                    samples_shifted = np.random.randint(low=1, high=int(
                        samples * 0.5 / 3))
                else:
                    samples_shifted = np.random.randint(low=1, high=int(
                        samples * 0.1 / 3))

                if method == 0:
                    shifted_samples = np.zeros((samples_shifted, ncha, 1))
                else:
                    if std == &#39;self&#39;:
                        std_applied = np.std(trial)
                    else:
                        std_applied = std
                    center = 0
                    shifted_samples = np.random.normal(center, std_applied,
                                                       (samples_shifted, ncha,
                                                        1))
                if position == 0:
                    trial = np.concatenate((shifted_samples, trial),
                                           axis=0)[:samples]
                else:
                    trial = np.concatenate((trial, shifted_samples),
                                           axis=0)[samples_shifted:]

            for _ in range(
                    augmentations[&#34;patch_perturbation&#34;]):  # Patch perturbation
                channels_affected = np.random.randint(low=1, high=ncha - 1)
                pct_max = 1
                pct_min = 0.2
                pct_erased = np.random.uniform(low=pct_min, high=pct_max)
                # Select time to be erased acording to pct_erased
                # samples_erased = np.min((int(samples*ncha*pct_erased//channels_affected), samples))#np.random.randint(low=1, high=samples//3)
                samples_erased = int(samples * pct_erased)
                # Select position where to erase that timeframe
                if samples_erased != samples:
                    samples_idx = np.arange(samples_erased) + np.random.randint(
                        samples - samples_erased)
                else:
                    samples_idx = np.arange(samples_erased)
                # Select indexes to erase (always keep at least a channel)
                channel_idx = np.random.permutation(np.arange(ncha))[
                              :channels_affected]
                channel_idx.sort()
                for channel in channel_idx:
                    if method == 0:
                        trial[samples_idx, channel] = 0
                    else:
                        if std == &#39;self&#39;:
                            std_applied = np.std(trial[:, channel]) \
                                          * np.random.uniform(low=0.01, high=2)
                        else:
                            std_applied = std
                        center = 0
                        trial[samples_idx, channel] += \
                            np.random.normal(center, std_applied,
                                             trial[samples_idx, channel,
                                             :].shape)
                        # Standarize the channel again after the change
                        temp_trial_ch_mean = np.mean(trial[:, channel], axis=0)
                        temp_trial_ch_std = np.std(trial[:, channel], axis=0)
                        trial[:, channel] = (trial[:,
                                             channel] - temp_trial_ch_mean) / temp_trial_ch_std

            for _ in range(augmentations[&#34;hemisphere_perturbation&#34;]):
                # Select side to mix/change for noise
                left_right = np.random.choice((0, 1))
                if method == 0:
                    if left_right == 1:
                        channel_idx = np.arange(ncha)[:int((ncha / 2) - 1)]
                        channel_mix = np.random.permutation(channel_idx.copy())
                    else:
                        channel_idx = np.arange(ncha)[-int((ncha / 2) - 1):]
                        channel_mix = np.random.permutation(channel_idx.copy())
                    temp_trial = trial.copy()
                    for channel, channel_mixed in zip(channel_idx, channel_mix):
                        temp_trial[:, channel] = trial[:, channel_mixed]
                    trial = temp_trial
                else:
                    if left_right == 1:
                        channel_idx = np.arange(ncha)[:int((ncha / 2) - 1)]
                    else:
                        channel_idx = np.arange(ncha)[-int((ncha / 2) - 1):]
                    for channel in channel_idx:
                        trial[:, channel] = np.random.normal(0, 1,
                                                             trial[:,
                                                             channel].shape)

        return trial

    return data_augmentation</code></pre>
</details>
</dd>
<dt id="EEGSym.EEGSym_DataAugmentation.trial_iterator"><code class="name flex">
<span>def <span class="ident">trial_iterator</span></span>(<span>X, y, batch_size=32, shuffle=True, augmentation=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Custom trial iterator to pretrain EEGSym.</p>
<h2 id="parameters">Parameters</h2>
<dl>
<dt><strong><code>X</code></strong> :&ensp;<code>tf.tensor</code></dt>
<dd>Input tensor of
EEG features.</dd>
<dt><strong><code>y</code></strong> :&ensp;<code>tf.tensor</code></dt>
<dd>Input tensor of
labels.</dd>
<dt><strong><code>batch_size</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of features in each batch.</dd>
<dt><strong><code>shuffle</code></strong> :&ensp;<code>Bool</code></dt>
<dd>If the features are shuffled at each training epoch.</dd>
<dt><strong><code>augmentation</code></strong> :&ensp;<code>Bool</code></dt>
<dd>If the augmentation is performed to the input.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><strong><code>trial_iterator</code></strong> :&ensp;<code>tf.keras.preprocessing.image.NumpyArrayIterator</code></dt>
<dd>Iterator used to train the model.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def trial_iterator(X, y, batch_size=32, shuffle=True, augmentation=True):
    &#34;&#34;&#34;Custom trial iterator to pretrain EEGSym.

        Parameters
        ----------
        X : tf.tensor
            Input tensor of  EEG features.
        y : tf.tensor
            Input tensor of  labels.
        batch_size : int
            Number of features in each batch.
        shuffle : Bool
            If the features are shuffled at each training epoch.
        augmentation : Bool
            If the augmentation is performed to the input.

        Returns
        -------
        trial_iterator : tf.keras.preprocessing.image.NumpyArrayIterator
            Iterator used to train the model.
    &#34;&#34;&#34;

    trial_data_generator = tf.keras.preprocessing.image.ImageDataGenerator(
        preprocessing_function=preprocessing_function(
            augmentation=augmentation))

    trial_iterator = tf.keras.preprocessing.image.NumpyArrayIterator(
        X, y, trial_data_generator, batch_size=batch_size, shuffle=shuffle,
        sample_weight=None,
        seed=None, data_format=None, save_to_dir=None, save_prefix=&#39;&#39;,
        save_format=&#39;png&#39;, subset=None, dtype=None
    )
    return trial_iterator</code></pre>
</details>
</dd>
</dl>
</section>
<section>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="EEGSym" href="index.html">EEGSym</a></code></li>
</ul>
</li>
<li><h3><a href="#header-functions">Functions</a></h3>
<ul class="">
<li><code><a title="EEGSym.EEGSym_DataAugmentation.preprocessing_function" href="#EEGSym.EEGSym_DataAugmentation.preprocessing_function">preprocessing_function</a></code></li>
<li><code><a title="EEGSym.EEGSym_DataAugmentation.trial_iterator" href="#EEGSym.EEGSym_DataAugmentation.trial_iterator">trial_iterator</a></code></li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>