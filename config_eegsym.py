# config_eegsym.py
# Configuration file for EEGSym training experiments

import numpy as np

class EEGSymConfig:
    """EEGSym训练配置类"""
    
    def __init__(self, config_name="default"):
        self.config_name = config_name
        self._set_default_config()
        
        # 根据配置名称设置特定参数
        if config_name == "quick_test":
            self._set_quick_test_config()
        elif config_name == "full_training":
            self._set_full_training_config()
        elif config_name == "pretrained_finetune":
            self._set_pretrained_finetune_config()
        elif config_name == "no_augmentation":
            self._set_no_augmentation_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        # 数据路径
        self.DATA_DIR = "E:\\SEED_session3\\SEED_pre3_normalized_shuffled"

        # 训练参数 - 针对GPU优化
        self.BATCH_SIZE = 16  # 减小批次大小以节省GPU内存
        self.EPOCHS = 10
        self.NUM_RUNS = 1
        self.LR = 1e-4
        self.USE_PRETRAINED = True
        self.USE_AUGMENTATION = True
        self.EARLY_STOPPING_PATIENCE = 25

        # GPU相关配置
        self.FORCE_GPU = True  # 强制使用GPU
        self.GPU_MEMORY_LIMIT = None  # GPU内存限制(MB)，None表示不限制
        self.MIXED_PRECISION = True  # 使用混合精度训练节省内存
        
        # 模型参数
        self.TARGET_FS = 250  # 目标采样率
        self.INPUT_TIME_SEC = 3  # 输入时间长度（秒）
        self.DROPOUT_RATE = 0.4
        self.ACTIVATION = 'elu'
        self.FILTERS_PER_BRANCH = 24
        self.SCALES_TIME = np.array([125, 250, 500])
        self.RESIDUAL = True
        
        # 数据分割
        self.TEST_SIZE = 0.2
        self.VAL_SIZE = 0.2  # 从训练集中分出的验证集比例
        
        # 其他设置
        self.VERBOSE = 1
        self.SAVE_MODEL = True
        self.RANDOM_SEED = 42
    
    def _set_quick_test_config(self):
        """快速测试配置（用于调试）"""
        self.EPOCHS = 5
        self.BATCH_SIZE = 8  # 更小的批次大小用于测试
        self.EARLY_STOPPING_PATIENCE = 3
        self.USE_PRETRAINED = False
        self.USE_AUGMENTATION = False
        self.GPU_MEMORY_LIMIT = 4096  # 4GB限制用于测试
        self.MIXED_PRECISION = False  # 测试时不使用混合精度
        print("Using quick test configuration")
    
    def _set_full_training_config(self):
        """完整训练配置"""
        self.EPOCHS = 100
        self.BATCH_SIZE = 12  # 减小批次大小以适应长时间训练
        self.NUM_RUNS = 5
        self.EARLY_STOPPING_PATIENCE = 30
        self.USE_PRETRAINED = True
        self.USE_AUGMENTATION = True
        self.MIXED_PRECISION = True  # 使用混合精度节省内存
        print("Using full training configuration")
    
    def _set_pretrained_finetune_config(self):
        """预训练微调配置"""
        self.EPOCHS = 30
        self.LR = 1e-5  # 更小的学习率用于微调
        self.USE_PRETRAINED = True
        self.USE_AUGMENTATION = True
        self.EARLY_STOPPING_PATIENCE = 15
        print("Using pretrained fine-tuning configuration")
    
    def _set_no_augmentation_config(self):
        """无数据增强配置"""
        self.USE_AUGMENTATION = False
        self.EPOCHS = 80  # 无增强时可能需要更多轮次
        print("Using no augmentation configuration")
    
    def get_eegsym_hyperparameters(self, ncha, n_classes):
        """获取EEGSym模型的超参数"""
        # 根据电极数量设置ch_lateral参数
        if ncha <= 16:
            ch_lateral = max(1, int((ncha / 2) - 1))
        else:
            ch_lateral = min(int(ncha / 4), 15)
        
        # 根据电极数量决定是否使用对称结构
        symmetric = True if ncha >= 8 else False
        
        hyperparameters = {
            "ncha": ncha,
            "dropout_rate": self.DROPOUT_RATE,
            "activation": self.ACTIVATION,
            "n_classes": n_classes,
            "learning_rate": self.LR,
            "fs": self.TARGET_FS,
            "input_time": self.INPUT_TIME_SEC * 1000,  # 转换为毫秒
            "scales_time": self.SCALES_TIME,
            "filters_per_branch": self.FILTERS_PER_BRANCH,
            "ch_lateral": ch_lateral,
            "residual": self.RESIDUAL,
            "symmetric": symmetric,
        }
        
        return hyperparameters
    
    def print_config(self):
        """打印当前配置"""
        print(f"\n{'='*50}")
        print(f"EEGSym Configuration: {self.config_name}")
        print(f"{'='*50}")
        
        print(f"Data Directory: {self.DATA_DIR}")
        print(f"Batch Size: {self.BATCH_SIZE}")
        print(f"Epochs: {self.EPOCHS}")
        print(f"Number of Runs: {self.NUM_RUNS}")
        print(f"Learning Rate: {self.LR}")
        print(f"Use Pretrained: {self.USE_PRETRAINED}")
        print(f"Use Augmentation: {self.USE_AUGMENTATION}")
        print(f"Early Stopping Patience: {self.EARLY_STOPPING_PATIENCE}")
        print(f"Target Sampling Rate: {self.TARGET_FS} Hz")
        print(f"Input Time Length: {self.INPUT_TIME_SEC} seconds")
        print(f"Dropout Rate: {self.DROPOUT_RATE}")
        print(f"Activation: {self.ACTIVATION}")
        print(f"Filters per Branch: {self.FILTERS_PER_BRANCH}")
        print(f"Time Scales: {self.SCALES_TIME}")
        print(f"Use Residual: {self.RESIDUAL}")
        print(f"Test Size: {self.TEST_SIZE}")
        print(f"Validation Size: {self.VAL_SIZE}")
        print(f"Random Seed: {self.RANDOM_SEED}")
        print(f"{'='*50}\n")

# 预定义的配置
CONFIGS = {
    "default": EEGSymConfig("default"),
    "quick_test": EEGSymConfig("quick_test"),
    "full_training": EEGSymConfig("full_training"),
    "pretrained_finetune": EEGSymConfig("pretrained_finetune"),
    "no_augmentation": EEGSymConfig("no_augmentation"),
}

def get_config(config_name="default"):
    """获取指定的配置"""
    if config_name in CONFIGS:
        return CONFIGS[config_name]
    else:
        print(f"Warning: Unknown config '{config_name}', using default")
        return CONFIGS["default"]

# 使用示例
if __name__ == "__main__":
    # 测试不同配置
    for config_name in CONFIGS.keys():
        config = get_config(config_name)
        config.print_config()
        
        # 示例：获取模型超参数
        hyperparams = config.get_eegsym_hyperparameters(ncha=62, n_classes=3)
        print("Model hyperparameters:")
        for key, value in hyperparams.items():
            print(f"  {key}: {value}")
        print("\n" + "-"*50 + "\n")
