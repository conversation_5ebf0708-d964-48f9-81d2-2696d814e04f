# EEGSym Training Script

这是一个基于EEGSym模型的训练脚本，改编自原始的`train15 copy.py`文件。该脚本将原本使用HCANN模型的情感识别任务迁移到EEGSym架构上。

## 主要特性

### 1. 模型架构
- **EEGSym**: 专为运动想象BCI设计的深度学习模型
- **对称架构**: 处理左右半球信息
- **多尺度时间卷积**: 捕获不同时间尺度的特征
- **残差连接**: 提高训练稳定性
- **数据增强**: 内置的EEG数据增强技术

### 2. 数据处理
- **输入格式转换**: 将`(N, 9, 62, 5)`格式转换为EEGSym所需的`(N, 时间点, 电极, 1)`格式
- **时间序列重构**: 通过插值将频带-时间窗数据转换为连续时间序列
- **标准化**: 保持与原始训练流程相同的数据预处理

### 3. 训练配置
- **多种预设配置**: 快速测试、完整训练、预训练微调等
- **命令行参数**: 灵活的参数覆盖
- **早停机制**: 防止过拟合
- **多次运行**: 统计稳定的结果

### 4. 评估指标
保持与原始脚本相同的评估指标：
- 准确率 (Accuracy)
- 精确率 (Precision)
- 召回率 (Recall)
- F1分数 (F1-Score)
- AUC (Area Under Curve)

## 文件结构

```
├── train_eegsym.py              # 主训练脚本
├── config_eegsym.py             # 配置管理
├── EEGSym_architecture.py       # EEGSym模型定义
├── EEGSym_DataAugmentation.py   # 数据增强功能
├── EEGSym_pretrained_weights_*.h5  # 预训练权重文件
└── README_eegsym_training.md    # 本说明文档
```

## 使用方法

### 基本使用

```bash
# 使用默认配置
python train_eegsym.py

# 使用快速测试配置（用于调试）
python train_eegsym.py --config quick_test

# 使用完整训练配置
python train_eegsym.py --config full_training

# 使用预训练微调配置
python train_eegsym.py --config pretrained_finetune
```

### 高级参数

```bash
# 自定义参数
python train_eegsym.py \
    --config default \
    --epochs 100 \
    --batch_size 64 \
    --lr 1e-5 \
    --num_runs 5 \
    --data_dir "path/to/your/data"

# 禁用预训练权重和数据增强
python train_eegsym.py \
    --config default \
    --no_pretrained \
    --no_augmentation
```

## 配置选项

### 预设配置

1. **default**: 标准配置，适合大多数情况
2. **quick_test**: 快速测试，用于调试和验证
3. **full_training**: 完整训练，多次运行获得稳定结果
4. **pretrained_finetune**: 预训练微调，使用更小的学习率
5. **no_augmentation**: 无数据增强配置

### 主要参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| BATCH_SIZE | 32 | 批次大小 |
| EPOCHS | 50 | 训练轮数 |
| LR | 1e-4 | 学习率 |
| USE_PRETRAINED | True | 是否使用预训练权重 |
| USE_AUGMENTATION | True | 是否使用数据增强 |
| TARGET_FS | 250 | 目标采样率 |
| INPUT_TIME_SEC | 3 | 输入时间长度（秒） |

## 数据格式要求

### 输入数据
- **X**: `(N, 9, 62, 5)` - (样本数, 频带, 电极, 时间窗)
- **Y**: `(N,)` - 标签数组

### 数据目录结构
```
data_directory/
├── subject_1_X.npy
├── subject_1_Y.npy
├── subject_2_X.npy
├── subject_2_Y.npy
...
├── subject_15_X.npy
└── subject_15_Y.npy
```

## 输出结果

### 训练过程输出
- 配置信息打印
- 数据加载和预处理信息
- 模型架构摘要
- 训练进度和验证结果
- 每次运行的详细指标

### 最终结果
```
Final Results (mean ± std):
--------------------------------------------------
  ACCURACY: 0.8234 ± 0.0156
 PRECISION: 0.8198 ± 0.0142
    RECALL: 0.8156 ± 0.0178
        F1: 0.8177 ± 0.0159
       AUC: 0.9012 ± 0.0098
```

### 保存的文件
- 模型权重: `eegsym_model_{config_name}_run_{run_number}.h5`

## 与原始脚本的对比

| 特性 | 原始脚本 (train15 copy.py) | EEGSym脚本 (train_eegsym.py) |
|------|---------------------------|------------------------------|
| 模型架构 | HCANN | EEGSym |
| 框架 | PyTorch | TensorFlow/Keras |
| 数据格式 | (N, 62, 45) | (N, 750, 62, 1) |
| 预训练权重 | 无 | 支持8/16电极预训练权重 |
| 数据增强 | 无 | 内置EEG数据增强 |
| 配置管理 | 硬编码 | 灵活的配置系统 |
| 命令行参数 | 无 | 完整的参数支持 |

## 注意事项

1. **依赖要求**: 确保安装了TensorFlow、scikit-learn、scipy等依赖
2. **GPU内存**: EEGSym模型可能需要较多GPU内存，建议使用GPU训练
3. **数据路径**: 根据实际情况修改数据路径
4. **预训练权重**: 8电极和16电极配置可以使用预训练权重，其他配置需要从头训练
5. **时间消耗**: 完整训练可能需要较长时间，建议先使用quick_test配置验证

## 故障排除

### 常见问题

1. **内存不足**: 减小batch_size或使用CPU训练
2. **数据格式错误**: 检查输入数据的维度和格式
3. **预训练权重加载失败**: 确保权重文件存在且电极数匹配
4. **收敛问题**: 调整学习率或增加训练轮数

### 调试建议

1. 使用`--config quick_test`进行快速验证
2. 检查数据加载和预处理步骤的输出
3. 监控训练过程中的损失和验证指标
4. 比较不同配置的结果

## 扩展功能

该脚本可以轻松扩展以支持：
- 不同的数据集格式
- 其他评估指标
- 模型集成方法
- 超参数优化
- 可视化功能
