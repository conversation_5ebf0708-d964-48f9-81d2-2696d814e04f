# test_eegsym_setup.py
# 测试EEGSym训练环境设置的脚本

import os
import sys
import numpy as np
import tensorflow as tf
from config_eegsym import get_config

def test_imports():
    """测试所有必要的导入"""
    print("Testing imports...")
    
    try:
        from EEGSym_architecture import EEGSym
        print("✓ EEGSym_architecture imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import EEGSym_architecture: {e}")
        return False
    
    try:
        from EEGSym_DataAugmentation import trial_iterator
        print("✓ EEGSym_DataAugmentation imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import EEGSym_DataAugmentation: {e}")
        return False
    
    try:
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        print("✓ sklearn imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import sklearn: {e}")
        return False
    
    try:
        from scipy import interpolate
        print("✓ scipy imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import scipy: {e}")
        return False
    
    return True

def test_tensorflow_gpu():
    """测试TensorFlow和GPU设置"""
    print("\nTesting TensorFlow and GPU...")

    print(f"TensorFlow version: {tf.__version__}")
    print(f"TensorFlow built with CUDA: {tf.test.is_built_with_cuda()}")

    # 检查GPU - 强制要求GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if not gpus:
        print("❌ No GPU found! This script requires GPU for training.")
        print("Please ensure you have:")
        print("1. NVIDIA GPU with CUDA support")
        print("2. Proper CUDA and cuDNN installation")
        print("3. TensorFlow-GPU installed")
        return False

    print(f"✓ Found {len(gpus)} GPU(s)")
    for i, gpu in enumerate(gpus):
        print(f"  GPU {i}: {gpu}")

    # 测试GPU内存增长设置
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print("✓ GPU memory growth configured successfully")
    except RuntimeError as e:
        print(f"✗ Failed to configure GPU memory growth: {e}")
        return False

    # 测试GPU计算
    try:
        with tf.device('/GPU:0'):
            test_tensor = tf.constant([[1.0, 2.0], [3.0, 4.0]])
            result = tf.matmul(test_tensor, test_tensor)
            print(f"✓ GPU computation test successful: {result.device}")
    except Exception as e:
        print(f"✗ GPU computation test failed: {e}")
        return False

    # 检查GPU内存
    try:
        memory_info = tf.config.experimental.get_memory_info('GPU:0')
        current_mb = memory_info['current'] / 1024**2
        peak_mb = memory_info['peak'] / 1024**2
        print(f"✓ GPU memory info: Current={current_mb:.1f}MB, Peak={peak_mb:.1f}MB")
    except Exception as e:
        print(f"⚠ Could not get GPU memory info: {e}")

    return True

def test_config_system():
    """测试配置系统"""
    print("\nTesting configuration system...")
    
    try:
        # 测试所有预设配置
        config_names = ['default', 'quick_test', 'full_training', 
                       'pretrained_finetune', 'no_augmentation']
        
        for config_name in config_names:
            config = get_config(config_name)
            print(f"✓ Config '{config_name}' loaded successfully")
            
            # 测试获取超参数
            hyperparams = config.get_eegsym_hyperparameters(ncha=62, n_classes=3)
            assert isinstance(hyperparams, dict)
            assert 'ncha' in hyperparams
            assert 'n_classes' in hyperparams
        
        print("✓ All configurations tested successfully")
        return True
        
    except Exception as e:
        print(f"✗ Configuration system test failed: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\nTesting model creation...")
    
    try:
        from EEGSym_architecture import EEGSym
        
        # 测试不同电极配置
        test_configs = [
            {'ncha': 8, 'n_classes': 2},
            {'ncha': 16, 'n_classes': 2},
            {'ncha': 62, 'n_classes': 3},
        ]
        
        for test_config in test_configs:
            ncha = test_config['ncha']
            n_classes = test_config['n_classes']
            
            # 获取配置
            config = get_config('quick_test')
            hyperparams = config.get_eegsym_hyperparameters(ncha, n_classes)
            
            # 创建模型
            model = EEGSym(**hyperparams)
            print(f"✓ Model created successfully for {ncha} channels, {n_classes} classes")
            
            # 测试模型输入输出
            input_samples = int(3 * config.TARGET_FS)  # 3秒数据
            test_input = np.random.randn(2, input_samples, ncha, 1)
            
            output = model.predict(test_input, verbose=0)
            expected_shape = (2, n_classes)
            
            if output.shape == expected_shape:
                print(f"✓ Model output shape correct: {output.shape}")
            else:
                print(f"✗ Model output shape incorrect: {output.shape}, expected: {expected_shape}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        return False

def test_data_preprocessing():
    """测试数据预处理"""
    print("\nTesting data preprocessing...")
    
    try:
        # 创建模拟数据
        N, freq_bands, channels, time_windows = 10, 9, 62, 5
        X_mock = np.random.randn(N, freq_bands, channels, time_windows)
        y_mock = np.random.randint(0, 3, N)
        
        print(f"Mock data created: X={X_mock.shape}, y={y_mock.shape}")
        
        # 导入数据预处理函数
        sys.path.append('.')
        from train_eegsym import reshape_for_eegsym
        
        # 测试数据重塑
        X_reshaped = reshape_for_eegsym(X_mock, target_fs=250)
        expected_shape = (N, 750, channels, 1)  # 3秒 * 250Hz = 750
        
        if X_reshaped.shape == expected_shape:
            print(f"✓ Data reshaping successful: {X_reshaped.shape}")
        else:
            print(f"✗ Data reshaping failed: {X_reshaped.shape}, expected: {expected_shape}")
            return False
        
        # 检查数据范围
        if np.isfinite(X_reshaped).all():
            print("✓ Reshaped data contains no NaN or infinite values")
        else:
            print("✗ Reshaped data contains NaN or infinite values")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Data preprocessing test failed: {e}")
        return False

def test_pretrained_weights():
    """测试预训练权重"""
    print("\nTesting pretrained weights...")
    
    pretrained_files = [
        'EEGSym_pretrained_weights_8_electrode.h5',
        'EEGSym_pretrained_weights_16_electrode.h5'
    ]
    
    found_weights = []
    for weight_file in pretrained_files:
        if os.path.exists(weight_file):
            print(f"✓ Found pretrained weights: {weight_file}")
            found_weights.append(weight_file)
        else:
            print(f"⚠ Pretrained weights not found: {weight_file}")
    
    if found_weights:
        print(f"✓ {len(found_weights)} pretrained weight file(s) available")
    else:
        print("⚠ No pretrained weights found (training will start from scratch)")
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("="*60)
    print("EEGSym Training Environment Test")
    print("="*60)
    
    tests = [
        ("Import Test", test_imports),
        ("TensorFlow/GPU Test", test_tensorflow_gpu),
        ("Configuration System Test", test_config_system),
        ("Model Creation Test", test_model_creation),
        ("Data Preprocessing Test", test_data_preprocessing),
        ("Pretrained Weights Test", test_pretrained_weights),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        print(f"Running {test_name}...")
        print(f"{'-'*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("Test Summary")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Environment is ready for training.")
        return True
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
