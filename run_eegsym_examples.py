# run_eegsym_examples.py
# 运行EEGSym训练的示例脚本

import os
import subprocess
import sys
import argparse

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=False, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"✗ {description} failed with error: {e}")
        return False

def run_environment_test():
    """运行环境测试"""
    return run_command("python test_eegsym_setup.py", "Environment Test")

def run_quick_test():
    """运行快速测试"""
    cmd = "python train_eegsym.py --config quick_test"
    return run_command(cmd, "Quick Test Training")

def run_default_training():
    """运行默认训练"""
    cmd = "python train_eegsym.py --config default"
    return run_command(cmd, "Default Training")

def run_pretrained_finetune():
    """运行预训练微调"""
    cmd = "python train_eegsym.py --config pretrained_finetune"
    return run_command(cmd, "Pretrained Fine-tuning")

def run_full_training():
    """运行完整训练"""
    cmd = "python train_eegsym.py --config full_training"
    return run_command(cmd, "Full Training (Multiple Runs)")

def run_no_augmentation():
    """运行无数据增强训练"""
    cmd = "python train_eegsym.py --config no_augmentation"
    return run_command(cmd, "Training without Data Augmentation")

def run_custom_training():
    """运行自定义参数训练"""
    cmd = ("python train_eegsym.py "
           "--config default "
           "--epochs 30 "
           "--batch_size 64 "
           "--lr 1e-5 "
           "--num_runs 3")
    return run_command(cmd, "Custom Parameters Training")

def main():
    parser = argparse.ArgumentParser(description='EEGSym Training Examples')
    parser.add_argument('--example', type=str, 
                       choices=['test', 'quick', 'default', 'pretrained', 
                               'full', 'no_aug', 'custom', 'all'],
                       default='test',
                       help='Which example to run')
    parser.add_argument('--data_dir', type=str, default=None,
                       help='Data directory path')
    
    args = parser.parse_args()
    
    # 检查数据目录
    if args.data_dir and not os.path.exists(args.data_dir):
        print(f"Error: Data directory '{args.data_dir}' does not exist")
        return False
    
    print("EEGSym Training Examples")
    print("="*60)
    
    if args.example == 'test':
        print("Running environment test...")
        return run_environment_test()
    
    elif args.example == 'quick':
        print("Running quick test training...")
        return run_quick_test()
    
    elif args.example == 'default':
        print("Running default training...")
        return run_default_training()
    
    elif args.example == 'pretrained':
        print("Running pretrained fine-tuning...")
        return run_pretrained_finetune()
    
    elif args.example == 'full':
        print("Running full training...")
        return run_full_training()
    
    elif args.example == 'no_aug':
        print("Running training without data augmentation...")
        return run_no_augmentation()
    
    elif args.example == 'custom':
        print("Running custom parameters training...")
        return run_custom_training()
    
    elif args.example == 'all':
        print("Running all examples...")
        
        examples = [
            ("Environment Test", run_environment_test),
            ("Quick Test", run_quick_test),
            ("Default Training", run_default_training),
            ("Pretrained Fine-tuning", run_pretrained_finetune),
            ("No Augmentation", run_no_augmentation),
            ("Custom Parameters", run_custom_training),
        ]
        
        results = []
        for name, func in examples:
            print(f"\n{'#'*60}")
            print(f"Starting: {name}")
            print(f"{'#'*60}")
            
            result = func()
            results.append((name, result))
            
            if not result:
                print(f"⚠ {name} failed, continuing with next example...")
        
        # 总结结果
        print(f"\n{'='*60}")
        print("All Examples Summary")
        print(f"{'='*60}")
        
        passed = 0
        for name, result in results:
            status = "PASS" if result else "FAIL"
            symbol = "✓" if result else "✗"
            print(f"{symbol} {name}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{len(results)} examples completed successfully")
        return passed == len(results)
    
    else:
        print(f"Unknown example: {args.example}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Example completed successfully!")
    else:
        print("\n⚠ Example completed with issues.")
    
    sys.exit(0 if success else 1)
