# gpu_monitor.py
# GPU内存监控工具

import tensorflow as tf
import psutil
import time
import threading
from datetime import datetime

class GPUMonitor:
    """GPU内存和使用率监控类"""
    
    def __init__(self, interval=5):
        self.interval = interval
        self.monitoring = False
        self.monitor_thread = None
        self.max_gpu_memory = 0
        self.max_cpu_memory = 0
        
    def get_gpu_info(self):
        """获取GPU信息"""
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if not gpus:
                return None
            
            # 获取GPU内存使用情况
            gpu_info = {}
            for i, gpu in enumerate(gpus):
                try:
                    # 尝试获取GPU内存信息
                    memory_info = tf.config.experimental.get_memory_info(f'GPU:{i}')
                    gpu_info[f'GPU_{i}'] = {
                        'current_mb': memory_info['current'] / 1024**2,
                        'peak_mb': memory_info['peak'] / 1024**2
                    }
                except:
                    # 如果无法获取详细信息，返回基本信息
                    gpu_info[f'GPU_{i}'] = {
                        'current_mb': 0,
                        'peak_mb': 0
                    }
            
            return gpu_info
            
        except Exception as e:
            print(f"Error getting GPU info: {e}")
            return None
    
    def get_cpu_memory_info(self):
        """获取CPU内存信息"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total_gb': memory.total / 1024**3,
                'used_gb': memory.used / 1024**3,
                'available_gb': memory.available / 1024**3,
                'percent': memory.percent
            }
        except Exception as e:
            print(f"Error getting CPU memory info: {e}")
            return None
    
    def print_status(self):
        """打印当前状态"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # GPU信息
        gpu_info = self.get_gpu_info()
        if gpu_info:
            print(f"\n[{timestamp}] GPU Memory Status:")
            for gpu_name, info in gpu_info.items():
                current_mb = info['current_mb']
                peak_mb = info['peak_mb']
                print(f"  {gpu_name}: Current={current_mb:.1f}MB, Peak={peak_mb:.1f}MB")
                
                # 更新最大值
                self.max_gpu_memory = max(self.max_gpu_memory, peak_mb)
        else:
            print(f"[{timestamp}] No GPU information available")
        
        # CPU内存信息
        cpu_info = self.get_cpu_memory_info()
        if cpu_info:
            print(f"[{timestamp}] CPU Memory: {cpu_info['used_gb']:.1f}GB/"
                  f"{cpu_info['total_gb']:.1f}GB ({cpu_info['percent']:.1f}%)")
            self.max_cpu_memory = max(self.max_cpu_memory, cpu_info['used_gb'])
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            self.print_status()
            time.sleep(self.interval)
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("Monitoring is already running")
            return
        
        print(f"Starting GPU/CPU memory monitoring (interval: {self.interval}s)")
        print("Press Ctrl+C to stop monitoring")
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
        
        print("\nStopping monitoring...")
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        
        print(f"\nMonitoring Summary:")
        print(f"  Max GPU Memory: {self.max_gpu_memory:.1f}MB")
        print(f"  Max CPU Memory: {self.max_cpu_memory:.1f}GB")
    
    def get_current_usage(self):
        """获取当前使用情况（用于程序中调用）"""
        gpu_info = self.get_gpu_info()
        cpu_info = self.get_cpu_memory_info()
        
        return {
            'gpu': gpu_info,
            'cpu': cpu_info,
            'timestamp': datetime.now()
        }

def check_gpu_requirements():
    """检查GPU要求"""
    print("Checking GPU requirements...")
    
    # 检查TensorFlow GPU支持
    print(f"TensorFlow version: {tf.__version__}")
    print(f"TensorFlow built with CUDA: {tf.test.is_built_with_cuda()}")
    
    # 检查GPU设备
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if not gpus:
        print("❌ No GPU devices found!")
        print("Requirements for GPU training:")
        print("1. NVIDIA GPU with CUDA Compute Capability 3.5+")
        print("2. CUDA 11.2+ installed")
        print("3. cuDNN 8.1+ installed")
        print("4. TensorFlow-GPU installed")
        return False
    
    print(f"✅ Found {len(gpus)} GPU device(s):")
    for i, gpu in enumerate(gpus):
        print(f"   GPU {i}: {gpu.name}")
    
    # 测试GPU可用性
    try:
        with tf.device('/GPU:0'):
            test_tensor = tf.constant([[1.0, 2.0], [3.0, 4.0]])
            result = tf.matmul(test_tensor, test_tensor)
            print(f"✅ GPU test successful: {result.device}")
        return True
    except Exception as e:
        print(f"❌ GPU test failed: {e}")
        return False

def estimate_memory_requirements(batch_size=16, n_samples=750, n_channels=62):
    """估算内存需求"""
    print(f"\nEstimating memory requirements:")
    print(f"  Batch size: {batch_size}")
    print(f"  Time samples: {n_samples}")
    print(f"  Channels: {n_channels}")
    
    # 估算输入数据大小
    input_size_mb = batch_size * n_samples * n_channels * 4 / 1024**2  # float32
    
    # 估算模型参数大小（EEGSym大约有100K-200K参数）
    model_size_mb = 200000 * 4 / 1024**2  # 假设200K参数
    
    # 估算梯度和优化器状态
    optimizer_size_mb = model_size_mb * 2  # Adam优化器需要额外内存
    
    # 估算中间激活
    activation_size_mb = input_size_mb * 10  # 粗略估计
    
    total_mb = input_size_mb + model_size_mb + optimizer_size_mb + activation_size_mb
    
    print(f"  Input data: {input_size_mb:.1f}MB")
    print(f"  Model parameters: {model_size_mb:.1f}MB")
    print(f"  Optimizer states: {optimizer_size_mb:.1f}MB")
    print(f"  Activations (estimated): {activation_size_mb:.1f}MB")
    print(f"  Total estimated: {total_mb:.1f}MB ({total_mb/1024:.1f}GB)")
    
    return total_mb

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='GPU Monitor Tool')
    parser.add_argument('--check', action='store_true', help='Check GPU requirements')
    parser.add_argument('--estimate', action='store_true', help='Estimate memory requirements')
    parser.add_argument('--monitor', action='store_true', help='Start monitoring')
    parser.add_argument('--interval', type=int, default=5, help='Monitoring interval in seconds')
    
    args = parser.parse_args()
    
    if args.check:
        check_gpu_requirements()
    
    if args.estimate:
        estimate_memory_requirements()
    
    if args.monitor:
        monitor = GPUMonitor(interval=args.interval)
        try:
            monitor.start_monitoring()
            # 保持程序运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            monitor.stop_monitoring()
    
    if not any([args.check, args.estimate, args.monitor]):
        print("GPU Monitor Tool")
        print("Usage:")
        print("  python gpu_monitor.py --check     # Check GPU requirements")
        print("  python gpu_monitor.py --estimate  # Estimate memory requirements")
        print("  python gpu_monitor.py --monitor   # Start monitoring")
