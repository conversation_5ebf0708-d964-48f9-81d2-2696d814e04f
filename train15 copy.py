# train15.py
import os
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score,
    precision_recall_fscore_support,
    roc_auc_score,
)
from tqdm import tqdm
from model import HCANN, get_variable

# ------- 超参数 -------
DATA_DIR   = "E:\SEED_session3\SEED_pre3_normalized_shuffled"      # 存放 subject_1_X.npy ... subject_15_Y.npy 的目录
BATCH_SIZE = 64
EPOCHS     = 10
NUM_RUNS   = 1
LR         = 1e-3
DEVICE     = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

def load_all_subjects_data(data_dir, num_subjects=15):
    X_list, y_list = [], []
    for sid in range(1, num_subjects+1):
        X = np.load(os.path.join(data_dir, f"subject_{sid}_X.npy"))  # (Ni,9,62,5)
        y = np.load(os.path.join(data_dir, f"subject_{sid}_Y.npy"))  # (Ni,)
        y = np.where(y == -1, 2, y)
        X_list.append(X)
        y_list.append(y)
        print(f"Subject {sid:2d}: X={X.shape}, y={y.shape}")
    X_all = np.concatenate(X_list, axis=0)
    y_all = np.concatenate(y_list, axis=0)
    print(f"Loaded total {X_all.shape[0]} samples")
    return X_all, y_all

def reshape_to_sequence(X):
    # 仍需保持 (N,9,62,5)→(N,62,9,5)→(N,62,45)
    return X.transpose(0, 2, 1, 3).reshape(X.shape[0], X.shape[2], -1)

def to_dataloader(X, y, batch_size, shuffle=False):
    X_tensor = torch.tensor(X, dtype=torch.float32)
    y_tensor = torch.tensor(y, dtype=torch.long)
    ds = TensorDataset(X_tensor, y_tensor)
    return DataLoader(ds, batch_size=batch_size, shuffle=shuffle, drop_last=False)

def train_one_epoch(model, loader, criterion, optimizer):
    model.train()
    total_loss = 0
    for Xb, yb in tqdm(loader, desc="Training", leave=False):
        Xb, yb = Xb.to(DEVICE), yb.to(DEVICE)
        optimizer.zero_grad()
        logits = model(Xb)
        loss = criterion(logits, yb)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * yb.size(0)
    return total_loss / len(loader.dataset)

def evaluate(model, loader):
    model.eval()
    y_true, y_pred, y_prob = [], [], []
    with torch.no_grad():
        for Xb, yb in loader:
            Xb = Xb.to(DEVICE)
            logits = model(Xb)
            probs = torch.softmax(logits, dim=1).cpu().numpy()
            preds = np.argmax(probs, axis=1)
            y_true.extend(yb.numpy())
            y_pred.extend(preds)
            y_prob.extend(probs)
    return np.array(y_true), np.array(y_pred), np.array(y_prob)

def main():
    global DEVICE
    # 打印GPU使用信息
    print(f"Using device: {DEVICE}")
    if torch.cuda.is_available():
        print(f"CUDA is available. GPU count: {torch.cuda.device_count()}")
        if DEVICE.type == 'cuda' and DEVICE.index < torch.cuda.device_count():
            print(f"GPU name: {torch.cuda.get_device_name(DEVICE)}")
            print(f"GPU memory: {torch.cuda.get_device_properties(DEVICE).total_memory / 1024**3:.1f} GB")
        else:
            print(f"Warning: Requested GPU {DEVICE.index} not available, falling back to cuda:0")
            DEVICE = torch.device("cuda:0")
            print(f"Now using device: {DEVICE}")
            print(f"GPU name: {torch.cuda.get_device_name(DEVICE)}")
            print(f"GPU memory: {torch.cuda.get_device_properties(DEVICE).total_memory / 1024**3:.1f} GB")
    else:
        print("CUDA is not available. Using CPU.")
    
    # 1. 加载
    X_all, y_all = load_all_subjects_data(DATA_DIR)
    X_all = reshape_to_sequence(X_all)  # (N,62,45)

    # —— 关键：dims 要对应 池化后 “宽度” —— 
    dim_initial = 64
    dims = [dim_initial // 4,        # 64//4 = 16
            dim_initial // 32]       # 64//32 = 2

    metrics = {m: [] for m in ["acc", "precision", "recall", "f1", "auc"]}

    for run in range(NUM_RUNS):
        seed = 42 + run
        np.random.seed(seed)
        torch.manual_seed(seed)

        X_train, X_test, y_train, y_test = train_test_split(
            X_all, y_all,
            test_size=0.2,
            stratify=y_all,
            random_state=seed,
        )
        train_loader = to_dataloader(X_train, y_train, BATCH_SIZE, shuffle=True)
        test_loader  = to_dataloader(X_test,  y_test,  BATCH_SIZE, shuffle=False)

        model = HCANN(
            fs=250,
            channels=9,
            dims=dims,          # ← restored to [16, 2]
            depth=[2, 2],
            heads=[2, 2],
            num_classes=3,
            dim_initial=dim_initial,
        ).to(DEVICE)

        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=LR)

        for epoch in tqdm(range(EPOCHS), desc=f"Run {run+1}/{NUM_RUNS}"):
            _ = train_one_epoch(model, train_loader, criterion, optimizer)

        y_true, y_pred, y_prob = evaluate(model, test_loader)
        acc = accuracy_score(y_true, y_pred)
        prec, rec, f1, _ = precision_recall_fscore_support(
            y_true, y_pred, average="macro", zero_division=0
        )
        auc = roc_auc_score(y_true, y_prob, multi_class="ovr", average="macro")

        metrics["acc"].append(acc)
        metrics["precision"].append(prec)
        metrics["recall"].append(rec)
        metrics["f1"].append(f1)
        metrics["auc"].append(auc)
        print(f"Run {run+1}/{NUM_RUNS} — "
              f"Acc={acc:.4f}, Prec={prec:.4f}, Rec={rec:.4f}, "
              f"F1={f1:.4f}, AUC={auc:.4f}")

    print("\nFinal Results (mean ± std):")
    for m, vals in metrics.items():
        arr = np.array(vals)
        print(f"{m.upper():>8}: {arr.mean():.4f} ± {arr.std():.4f}")

if __name__ == "__main__":
    main()
