Model: "EEGSym_16electrode"
__________________________________________________________________________________________________
Layer (type)                    Output Shape         Param #     Connected to                     
==================================================================================================
input_3 (InputLayer)            [(None, 384, 16, 1)] 0                                            
__________________________________________________________________________________________________
tf.expand_dims_2 (TFOpLambda)   (None, 1, 384, 16, 1 0           input_3[0][0]                    
__________________________________________________________________________________________________
tf.compat.v1.gather_3 (TFOpLamb (None, 1, 384, 7, 1) 0           tf.expand_dims_2[0][0]           
__________________________________________________________________________________________________
tf.compat.v1.gather_5 (TFOpLamb (None, 1, 384, 2, 1) 0           tf.expand_dims_2[0][0]           
__________________________________________________________________________________________________
tf.compat.v1.gather_4 (TFOpLamb (None, 1, 384, 7, 1) 0           tf.expand_dims_2[0][0]           
__________________________________________________________________________________________________
concatenate_7 (Concatenate)     (None, 1, 384, 9, 1) 0           tf.compat.v1.gather_3[0][0]      
                                                                 tf.compat.v1.gather_5[0][0]      
__________________________________________________________________________________________________
concatenate_8 (Concatenate)     (None, 1, 384, 9, 1) 0           tf.compat.v1.gather_4[0][0]      
                                                                 tf.compat.v1.gather_5[0][0]      
__________________________________________________________________________________________________
concatenate_9 (Concatenate)     (None, 2, 384, 9, 1) 0           concatenate_7[0][0]              
                                                                 concatenate_8[0][0]              
__________________________________________________________________________________________________
conv3d_60 (Conv3D)              (None, 2, 384, 9, 24 408         concatenate_9[0][0]              
__________________________________________________________________________________________________
conv3d_61 (Conv3D)              (None, 2, 384, 9, 24 792         concatenate_9[0][0]              
__________________________________________________________________________________________________
conv3d_62 (Conv3D)              (None, 2, 384, 9, 24 1560        concatenate_9[0][0]              
__________________________________________________________________________________________________
batch_normalization_60 (BatchNo (None, 2, 384, 9, 24 96          conv3d_60[0][0]                  
__________________________________________________________________________________________________
batch_normalization_61 (BatchNo (None, 2, 384, 9, 24 96          conv3d_61[0][0]                  
__________________________________________________________________________________________________
batch_normalization_62 (BatchNo (None, 2, 384, 9, 24 96          conv3d_62[0][0]                  
__________________________________________________________________________________________________
activation_39 (Activation)      (None, 2, 384, 9, 24 0           batch_normalization_60[0][0]     
__________________________________________________________________________________________________
activation_40 (Activation)      (None, 2, 384, 9, 24 0           batch_normalization_61[0][0]     
__________________________________________________________________________________________________
activation_41 (Activation)      (None, 2, 384, 9, 24 0           batch_normalization_62[0][0]     
__________________________________________________________________________________________________
dropout_39 (Dropout)            (None, 2, 384, 9, 24 0           activation_39[0][0]              
__________________________________________________________________________________________________
dropout_40 (Dropout)            (None, 2, 384, 9, 24 0           activation_40[0][0]              
__________________________________________________________________________________________________
dropout_41 (Dropout)            (None, 2, 384, 9, 24 0           activation_41[0][0]              
__________________________________________________________________________________________________
concatenate_10 (Concatenate)    (None, 2, 384, 9, 72 0           dropout_39[0][0]                 
                                                                 dropout_40[0][0]                 
                                                                 dropout_41[0][0]                 
__________________________________________________________________________________________________
add_18 (Add)                    (None, 2, 384, 9, 72 0           concatenate_10[0][0]             
                                                                 concatenate_9[0][0]              
__________________________________________________________________________________________________
average_pooling3d_12 (AveragePo (None, 2, 192, 9, 72 0           add_18[0][0]                     
__________________________________________________________________________________________________
conv3d_63 (Conv3D)              (None, 2, 192, 1, 72 648         average_pooling3d_12[0][0]       
__________________________________________________________________________________________________
batch_normalization_63 (BatchNo (None, 2, 192, 1, 72 288         conv3d_63[0][0]                  
__________________________________________________________________________________________________
activation_42 (Activation)      (None, 2, 192, 1, 72 0           batch_normalization_63[0][0]     
__________________________________________________________________________________________________
dropout_42 (Dropout)            (None, 2, 192, 1, 72 0           activation_42[0][0]              
__________________________________________________________________________________________________
add_19 (Add)                    (None, 2, 192, 9, 72 0           average_pooling3d_12[0][0]       
                                                                 dropout_42[0][0]                 
__________________________________________________________________________________________________
conv3d_66 (Conv3D)              (None, 2, 192, 9, 24 6936        add_19[0][0]                     
__________________________________________________________________________________________________
conv3d_67 (Conv3D)              (None, 2, 192, 9, 24 13848       add_19[0][0]                     
__________________________________________________________________________________________________
conv3d_68 (Conv3D)              (None, 2, 192, 9, 24 27672       add_19[0][0]                     
__________________________________________________________________________________________________
batch_normalization_66 (BatchNo (None, 2, 192, 9, 24 96          conv3d_66[0][0]                  
__________________________________________________________________________________________________
batch_normalization_67 (BatchNo (None, 2, 192, 9, 24 96          conv3d_67[0][0]                  
__________________________________________________________________________________________________
batch_normalization_68 (BatchNo (None, 2, 192, 9, 24 96          conv3d_68[0][0]                  
__________________________________________________________________________________________________
activation_43 (Activation)      (None, 2, 192, 9, 24 0           batch_normalization_66[0][0]     
__________________________________________________________________________________________________
activation_44 (Activation)      (None, 2, 192, 9, 24 0           batch_normalization_67[0][0]     
__________________________________________________________________________________________________
activation_45 (Activation)      (None, 2, 192, 9, 24 0           batch_normalization_68[0][0]     
__________________________________________________________________________________________________
dropout_43 (Dropout)            (None, 2, 192, 9, 24 0           activation_43[0][0]              
__________________________________________________________________________________________________
dropout_44 (Dropout)            (None, 2, 192, 9, 24 0           activation_44[0][0]              
__________________________________________________________________________________________________
dropout_45 (Dropout)            (None, 2, 192, 9, 24 0           activation_45[0][0]              
__________________________________________________________________________________________________
concatenate_11 (Concatenate)    (None, 2, 192, 9, 72 0           dropout_43[0][0]                 
                                                                 dropout_44[0][0]                 
                                                                 dropout_45[0][0]                 
__________________________________________________________________________________________________
add_20 (Add)                    (None, 2, 192, 9, 72 0           concatenate_11[0][0]             
                                                                 add_19[0][0]                     
__________________________________________________________________________________________________
average_pooling3d_13 (AveragePo (None, 2, 96, 9, 72) 0           add_20[0][0]                     
__________________________________________________________________________________________________
conv3d_69 (Conv3D)              (None, 2, 96, 1, 72) 648         average_pooling3d_13[0][0]       
__________________________________________________________________________________________________
batch_normalization_69 (BatchNo (None, 2, 96, 1, 72) 288         conv3d_69[0][0]                  
__________________________________________________________________________________________________
activation_46 (Activation)      (None, 2, 96, 1, 72) 0           batch_normalization_69[0][0]     
__________________________________________________________________________________________________
dropout_46 (Dropout)            (None, 2, 96, 1, 72) 0           activation_46[0][0]              
__________________________________________________________________________________________________
add_21 (Add)                    (None, 2, 96, 9, 72) 0           average_pooling3d_13[0][0]       
                                                                 dropout_46[0][0]                 
__________________________________________________________________________________________________
conv3d_72 (Conv3D)              (None, 2, 96, 9, 36) 41508       add_21[0][0]                     
__________________________________________________________________________________________________
conv3d_75 (Conv3D)              (None, 2, 96, 9, 36) 2592        add_21[0][0]                     
__________________________________________________________________________________________________
batch_normalization_72 (BatchNo (None, 2, 96, 9, 36) 144         conv3d_72[0][0]                  
__________________________________________________________________________________________________
batch_normalization_75 (BatchNo (None, 2, 96, 9, 36) 144         conv3d_75[0][0]                  
__________________________________________________________________________________________________
activation_47 (Activation)      (None, 2, 96, 9, 36) 0           batch_normalization_72[0][0]     
__________________________________________________________________________________________________
activation_48 (Activation)      (None, 2, 96, 9, 36) 0           batch_normalization_75[0][0]     
__________________________________________________________________________________________________
dropout_47 (Dropout)            (None, 2, 96, 9, 36) 0           activation_47[0][0]              
__________________________________________________________________________________________________
dropout_48 (Dropout)            (None, 2, 96, 9, 36) 0           activation_48[0][0]              
__________________________________________________________________________________________________
add_22 (Add)                    (None, 2, 96, 9, 36) 0           dropout_47[0][0]                 
                                                                 dropout_48[0][0]                 
__________________________________________________________________________________________________
average_pooling3d_14 (AveragePo (None, 2, 48, 9, 36) 0           add_22[0][0]                     
__________________________________________________________________________________________________
conv3d_74 (Conv3D)              (None, 2, 48, 1, 36) 11664       average_pooling3d_14[0][0]       
__________________________________________________________________________________________________
batch_normalization_74 (BatchNo (None, 2, 48, 1, 36) 144         conv3d_74[0][0]                  
__________________________________________________________________________________________________
activation_49 (Activation)      (None, 2, 48, 1, 36) 0           batch_normalization_74[0][0]     
__________________________________________________________________________________________________
dropout_49 (Dropout)            (None, 2, 48, 1, 36) 0           activation_49[0][0]              
__________________________________________________________________________________________________
add_23 (Add)                    (None, 2, 48, 9, 36) 0           average_pooling3d_14[0][0]       
                                                                 dropout_49[0][0]                 
__________________________________________________________________________________________________
conv3d_76 (Conv3D)              (None, 2, 48, 9, 36) 10404       add_23[0][0]                     
__________________________________________________________________________________________________
conv3d_79 (Conv3D)              (None, 2, 48, 9, 36) 1296        add_23[0][0]                     
__________________________________________________________________________________________________
batch_normalization_76 (BatchNo (None, 2, 48, 9, 36) 144         conv3d_76[0][0]                  
__________________________________________________________________________________________________
batch_normalization_79 (BatchNo (None, 2, 48, 9, 36) 144         conv3d_79[0][0]                  
__________________________________________________________________________________________________
activation_50 (Activation)      (None, 2, 48, 9, 36) 0           batch_normalization_76[0][0]     
__________________________________________________________________________________________________
activation_51 (Activation)      (None, 2, 48, 9, 36) 0           batch_normalization_79[0][0]     
__________________________________________________________________________________________________
dropout_50 (Dropout)            (None, 2, 48, 9, 36) 0           activation_50[0][0]              
__________________________________________________________________________________________________
dropout_51 (Dropout)            (None, 2, 48, 9, 36) 0           activation_51[0][0]              
__________________________________________________________________________________________________
add_24 (Add)                    (None, 2, 48, 9, 36) 0           dropout_50[0][0]                 
                                                                 dropout_51[0][0]                 
__________________________________________________________________________________________________
average_pooling3d_15 (AveragePo (None, 2, 24, 9, 36) 0           add_24[0][0]                     
__________________________________________________________________________________________________
conv3d_78 (Conv3D)              (None, 2, 24, 1, 36) 11664       average_pooling3d_15[0][0]       
__________________________________________________________________________________________________
batch_normalization_78 (BatchNo (None, 2, 24, 1, 36) 144         conv3d_78[0][0]                  
__________________________________________________________________________________________________
activation_52 (Activation)      (None, 2, 24, 1, 36) 0           batch_normalization_78[0][0]     
__________________________________________________________________________________________________
dropout_52 (Dropout)            (None, 2, 24, 1, 36) 0           activation_52[0][0]              
__________________________________________________________________________________________________
add_25 (Add)                    (None, 2, 24, 9, 36) 0           average_pooling3d_15[0][0]       
                                                                 dropout_52[0][0]                 
__________________________________________________________________________________________________
conv3d_80 (Conv3D)              (None, 2, 24, 9, 18) 2610        add_25[0][0]                     
__________________________________________________________________________________________________
conv3d_83 (Conv3D)              (None, 2, 24, 9, 18) 648         add_25[0][0]                     
__________________________________________________________________________________________________
batch_normalization_80 (BatchNo (None, 2, 24, 9, 18) 72          conv3d_80[0][0]                  
__________________________________________________________________________________________________
batch_normalization_83 (BatchNo (None, 2, 24, 9, 18) 72          conv3d_83[0][0]                  
__________________________________________________________________________________________________
activation_53 (Activation)      (None, 2, 24, 9, 18) 0           batch_normalization_80[0][0]     
__________________________________________________________________________________________________
activation_54 (Activation)      (None, 2, 24, 9, 18) 0           batch_normalization_83[0][0]     
__________________________________________________________________________________________________
dropout_53 (Dropout)            (None, 2, 24, 9, 18) 0           activation_53[0][0]              
__________________________________________________________________________________________________
dropout_54 (Dropout)            (None, 2, 24, 9, 18) 0           activation_54[0][0]              
__________________________________________________________________________________________________
add_26 (Add)                    (None, 2, 24, 9, 18) 0           dropout_53[0][0]                 
                                                                 dropout_54[0][0]                 
__________________________________________________________________________________________________
average_pooling3d_16 (AveragePo (None, 2, 12, 9, 18) 0           add_26[0][0]                     
__________________________________________________________________________________________________
conv3d_82 (Conv3D)              (None, 2, 12, 1, 18) 2916        average_pooling3d_16[0][0]       
__________________________________________________________________________________________________
batch_normalization_82 (BatchNo (None, 2, 12, 1, 18) 72          conv3d_82[0][0]                  
__________________________________________________________________________________________________
activation_55 (Activation)      (None, 2, 12, 1, 18) 0           batch_normalization_82[0][0]     
__________________________________________________________________________________________________
dropout_55 (Dropout)            (None, 2, 12, 1, 18) 0           activation_55[0][0]              
__________________________________________________________________________________________________
add_27 (Add)                    (None, 2, 12, 9, 18) 0           average_pooling3d_16[0][0]       
                                                                 dropout_55[0][0]                 
__________________________________________________________________________________________________
conv3d_84 (Conv3D)              (None, 2, 12, 9, 18) 1296        add_27[0][0]                     
__________________________________________________________________________________________________
batch_normalization_84 (BatchNo (None, 2, 12, 9, 18) 72          conv3d_84[0][0]                  
__________________________________________________________________________________________________
activation_56 (Activation)      (None, 2, 12, 9, 18) 0           batch_normalization_84[0][0]     
__________________________________________________________________________________________________
dropout_56 (Dropout)            (None, 2, 12, 9, 18) 0           activation_56[0][0]              
__________________________________________________________________________________________________
add_28 (Add)                    (None, 2, 12, 9, 18) 0           add_27[0][0]                     
                                                                 dropout_56[0][0]                 
__________________________________________________________________________________________________
average_pooling3d_17 (AveragePo (None, 2, 6, 9, 18)  0           add_28[0][0]                     
__________________________________________________________________________________________________
conv3d_85 (Conv3D)              (None, 1, 6, 1, 18)  5832        average_pooling3d_17[0][0]       
__________________________________________________________________________________________________
batch_normalization_85 (BatchNo (None, 1, 6, 1, 18)  72          conv3d_85[0][0]                  
__________________________________________________________________________________________________
activation_57 (Activation)      (None, 1, 6, 1, 18)  0           batch_normalization_85[0][0]     
__________________________________________________________________________________________________
dropout_57 (Dropout)            (None, 1, 6, 1, 18)  0           activation_57[0][0]              
__________________________________________________________________________________________________
add_29 (Add)                    (None, 2, 6, 9, 18)  0           average_pooling3d_17[0][0]       
                                                                 dropout_57[0][0]                 
__________________________________________________________________________________________________
conv3d_86 (Conv3D)              (None, 1, 6, 1, 18)  5832        add_29[0][0]                     
__________________________________________________________________________________________________
batch_normalization_86 (BatchNo (None, 1, 6, 1, 18)  72          conv3d_86[0][0]                  
__________________________________________________________________________________________________
activation_58 (Activation)      (None, 1, 6, 1, 18)  0           batch_normalization_86[0][0]     
__________________________________________________________________________________________________
dropout_58 (Dropout)            (None, 1, 6, 1, 18)  0           activation_58[0][0]              
__________________________________________________________________________________________________
add_30 (Add)                    (None, 2, 6, 9, 18)  0           add_29[0][0]                     
                                                                 dropout_58[0][0]                 
__________________________________________________________________________________________________
conv3d_87 (Conv3D)              (None, 1, 6, 1, 18)  648         add_30[0][0]                     
__________________________________________________________________________________________________
batch_normalization_87 (BatchNo (None, 1, 6, 1, 18)  72          conv3d_87[0][0]                  
__________________________________________________________________________________________________
activation_59 (Activation)      (None, 1, 6, 1, 18)  0           batch_normalization_87[0][0]     
__________________________________________________________________________________________________
dropout_59 (Dropout)            (None, 1, 6, 1, 18)  0           activation_59[0][0]              
__________________________________________________________________________________________________
conv3d_88 (Conv3D)              (None, 1, 1, 1, 18)  1944        dropout_59[0][0]                 
__________________________________________________________________________________________________
batch_normalization_88 (BatchNo (None, 1, 1, 1, 18)  72          conv3d_88[0][0]                  
__________________________________________________________________________________________________
activation_60 (Activation)      (None, 1, 1, 1, 18)  0           batch_normalization_88[0][0]     
__________________________________________________________________________________________________
dropout_60 (Dropout)            (None, 1, 1, 1, 18)  0           activation_60[0][0]              
__________________________________________________________________________________________________
add_31 (Add)                    (None, 1, 6, 1, 18)  0           dropout_59[0][0]                 
                                                                 dropout_60[0][0]                 
__________________________________________________________________________________________________
conv3d_89 (Conv3D)              (None, 1, 1, 1, 36)  216         add_31[0][0]                     
__________________________________________________________________________________________________
batch_normalization_89 (BatchNo (None, 1, 1, 1, 36)  144         conv3d_89[0][0]                  
__________________________________________________________________________________________________
activation_61 (Activation)      (None, 1, 1, 1, 36)  0           batch_normalization_89[0][0]     
__________________________________________________________________________________________________
dropout_61 (Dropout)            (None, 1, 1, 1, 36)  0           activation_61[0][0]              
__________________________________________________________________________________________________
conv3d_90 (Conv3D)              (None, 1, 1, 1, 36)  1296        dropout_61[0][0]                 
__________________________________________________________________________________________________
batch_normalization_90 (BatchNo (None, 1, 1, 1, 36)  144         conv3d_90[0][0]                  
__________________________________________________________________________________________________
activation_62 (Activation)      (None, 1, 1, 1, 36)  0           batch_normalization_90[0][0]     
__________________________________________________________________________________________________
dropout_62 (Dropout)            (None, 1, 1, 1, 36)  0           activation_62[0][0]              
__________________________________________________________________________________________________
add_32 (Add)                    (None, 1, 1, 1, 36)  0           dropout_61[0][0]                 
                                                                 dropout_62[0][0]                 
__________________________________________________________________________________________________
conv3d_91 (Conv3D)              (None, 1, 1, 1, 36)  1296        add_32[0][0]                     
__________________________________________________________________________________________________
batch_normalization_91 (BatchNo (None, 1, 1, 1, 36)  144         conv3d_91[0][0]                  
__________________________________________________________________________________________________
activation_63 (Activation)      (None, 1, 1, 1, 36)  0           batch_normalization_91[0][0]     
__________________________________________________________________________________________________
dropout_63 (Dropout)            (None, 1, 1, 1, 36)  0           activation_63[0][0]              
__________________________________________________________________________________________________
add_33 (Add)                    (None, 1, 1, 1, 36)  0           add_32[0][0]                     
                                                                 dropout_63[0][0]                 
__________________________________________________________________________________________________
conv3d_92 (Conv3D)              (None, 1, 1, 1, 36)  1296        add_33[0][0]                     
__________________________________________________________________________________________________
batch_normalization_92 (BatchNo (None, 1, 1, 1, 36)  144         conv3d_92[0][0]                  
__________________________________________________________________________________________________
activation_64 (Activation)      (None, 1, 1, 1, 36)  0           batch_normalization_92[0][0]     
__________________________________________________________________________________________________
dropout_64 (Dropout)            (None, 1, 1, 1, 36)  0           activation_64[0][0]              
__________________________________________________________________________________________________
add_34 (Add)                    (None, 1, 1, 1, 36)  0           add_33[0][0]                     
                                                                 dropout_64[0][0]                 
__________________________________________________________________________________________________
conv3d_93 (Conv3D)              (None, 1, 1, 1, 36)  1296        add_34[0][0]                     
__________________________________________________________________________________________________
batch_normalization_93 (BatchNo (None, 1, 1, 1, 36)  144         conv3d_93[0][0]                  
__________________________________________________________________________________________________
activation_65 (Activation)      (None, 1, 1, 1, 36)  0           batch_normalization_93[0][0]     
__________________________________________________________________________________________________
dropout_65 (Dropout)            (None, 1, 1, 1, 36)  0           activation_65[0][0]              
__________________________________________________________________________________________________
add_35 (Add)                    (None, 1, 1, 1, 36)  0           add_34[0][0]                     
                                                                 dropout_65[0][0]                 
__________________________________________________________________________________________________
flatten_2 (Flatten)             (None, 36)           0           add_35[0][0]                     
__________________________________________________________________________________________________
dense_2 (Dense)                 (None, 2)            74          flatten_2[0][0]                  
==================================================================================================
Total params: 162,152
Trainable params: 160,496
Non-trainable params: 1,656
__________________________________________________________________________________________________
