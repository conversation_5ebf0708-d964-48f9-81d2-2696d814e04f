<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>EEGSym.EEGSym_architecture API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>EEGSym.EEGSym_architecture</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">from tensorflow.keras.layers import Activation, Input, Flatten
from tensorflow.keras.layers import Dropout, BatchNormalization
from tensorflow.keras.layers import Conv3D, Add, AveragePooling3D
from tensorflow.keras.layers import Dense
import tensorflow.keras as keras
import tensorflow as tf
import numpy as np

def EEGSym(input_time=3000, fs=128, ncha=8, filters_per_branch=8,
           scales_time=(500, 250, 125), dropout_rate=0.25, activation=&#39;elu&#39;,
           n_classes=2, learning_rate=0.001, ch_lateral=3,
           spatial_resnet_repetitions=1, residual=True, symmetric=True):

    &#34;&#34;&#34;Keras implementation of EEGSym.

    This model was initially designed for MI decodification of either
    left/right hand.
    Hyperparameters and architectural choices are explained in the
    original article.

    Parameters
    ----------
    input_time : int
        EEG epoch time in milliseconds.
    fs : int
        Sample rate of the EEG.
    ncha :
        Number of input channels.
    filters_per_branch : int
        Number of filters in each Inception branch. The number should be
        multiplies of 8.
    scales_time : list
        Temporal scale of the temporal convolutions on first Inception module.
        This parameter determines the kernel sizes of the filters.
    dropout_rate : float
        Dropout rate
    activation : str
        Activation
    n_classes : int
        Number of output classes
    learning_rate : float
        Learning rate
    ch_lateral : int
        Number of channels that are attributed to one hemisphere of the head.
    spatial_resnet_repetitions: int
        Number of repetitions of the operations of spatial analysis at each
        step of the spatiotemporal analysis. In the original publication this
        value was set to 1 and not tested its variations.
    residual : Bool
        If the residual operations are present in EEGSym architecture.
    symmetric : Bool
        If the architecture considers the parameter ch_lateral to create two
        symmetric inputs of the electrodes.

    Returns
    -------
    model : keras.models.Model
        Keras model already compiled and ready to work
    &#34;&#34;&#34;

    # ======================================================================== #
    # ================== GENERAL INCEPTION/RESIDUAL MODULE =================== #
    def general_module(input, scales_samples, filters_per_branch, ncha,
                         activation, dropout_rate, average,
                         spatial_resnet_repetitions=1, residual=True,
                         init=False):
        &#34;&#34;&#34;General inception/residual module.

            This function returns the input with the operations of a
            inception or residual module from the publication applied.

            Parameters
            ----------
            input : list
                List of input blocks to the module.
            scales_samples : list
                List of samples size of the temporal operations kernels.
            filters_per_branch : int
                Number of filters in each Inception branch. The number should be
                multiplies of 8.
            ncha :
                Number of input channels.
            activation : str
                Activation
            dropout_rate : float
                Dropout rate
            spatial_resnet_repetitions: int
                Number of repetitions of the operations of spatial analysis at
                each step of the spatiotemporal analysis. In the original
                publication this value was set to 1 and not tested its
                variations.
            residual : Bool
                If the residual operations are present in EEGSym architecture.
            init : Bool
                If the module is the first one applied to the input, to apply a
                channel merging operation if the architecture does not include
                residual operations.

            Returns
            -------
            block_out : list
                List of outputs modules
        &#34;&#34;&#34;
        block_units = list()
        unit_conv_t = list()
        unit_batchconv_t = list()

        for i in range(len(scales_samples)):
            unit_conv_t.append(Conv3D(filters=filters_per_branch,
                                      kernel_size=(1, scales_samples[i], 1),
                                      kernel_initializer=&#39;he_normal&#39;,
                                      padding=&#39;same&#39;))
            unit_batchconv_t.append(BatchNormalization())

        if ncha != 1:
            unit_dconv = list()
            unit_batchdconv = list()
            unit_conv_s = list()
            unit_batchconv_s = list()
            for i in range(spatial_resnet_repetitions):
                # 3D Implementation of DepthwiseConv
                unit_dconv.append(Conv3D(kernel_size=(1, 1, ncha),
                                         filters=filters_per_branch * len(
                                             scales_samples),
                                         groups=filters_per_branch * len(
                                             scales_samples),
                                         use_bias=False,
                                         padding=&#39;valid&#39;))
                unit_batchdconv.append(BatchNormalization())

                unit_conv_s.append(Conv3D(kernel_size=(1, 1, ncha),
                                          filters=filters_per_branch,
                                          # groups=filters_per_branch,
                                          use_bias=False,
                                          strides=(1, 1, 1),
                                          kernel_initializer=&#39;he_normal&#39;,
                                          padding=&#39;valid&#39;))
                unit_batchconv_s.append(BatchNormalization())

            unit_conv_1 = Conv3D(kernel_size=(1, 1, 1),
                                 filters=filters_per_branch,
                                 use_bias=False,
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;valid&#39;)
            unit_batchconv_1 = BatchNormalization()

        for j in range(len(input)):
            block_side_units = list()
            for i in range(len(scales_samples)):
                unit = input[j]
                unit = unit_conv_t[i](unit)

                unit = unit_batchconv_t[i](unit)
                unit = Activation(activation)(unit)
                unit = Dropout(dropout_rate)(unit)

                block_side_units.append(unit)
            block_units.append(block_side_units)
        # Concatenation
        block_out = list()
        for j in range(len(input)):
            if len(block_units[j]) != 1:
                block_out.append(
                    keras.layers.concatenate(block_units[j], axis=-1))
            else:
                block_out.append(block_units[j][0])

            if residual:
                if len(block_units[j]) != 1:
                    block_out_temp = input[j]
                else:
                    block_out_temp = input[j]
                    block_out_temp = unit_conv_1(block_out_temp)

                    block_out_temp = unit_batchconv_1(block_out_temp)
                    block_out_temp = Activation(activation)(block_out_temp)
                    block_out_temp = Dropout(dropout_rate)(block_out_temp)

                block_out[j] = Add()([block_out[j], block_out_temp])

            if average != 1:
                block_out[j] = AveragePooling3D((1, average, 1))(block_out[j])

        if ncha != 1:
            for i in range(spatial_resnet_repetitions):
                block_out_temp = list()
                for j in range(len(input)):
                    if len(scales_samples) != 1:
                        if residual:
                            block_out_temp.append(block_out[j])

                            block_out_temp[j] = unit_dconv[i](block_out_temp[j])

                            block_out_temp[j] = unit_batchdconv[i](
                                block_out_temp[j])
                            block_out_temp[j] = Activation(activation)(
                                block_out_temp[j])
                            block_out_temp[j] = Dropout(dropout_rate)(
                                block_out_temp[j])

                            block_out[j] = Add()(
                                [block_out[j], block_out_temp[j]])

                        elif init:
                            block_out[j] = unit_dconv[i](block_out[j])
                            block_out[j] = unit_batchdconv[i](block_out[j])
                            block_out[j] = Activation(activation)(block_out[j])
                            block_out[j] = Dropout(dropout_rate)(block_out[j])
                    else:
                        if residual:
                            block_out_temp.append(block_out[j])

                            block_out_temp[j] = unit_conv_s[i](
                                block_out_temp[j])
                            block_out_temp[j] = unit_batchconv_s[i](
                                block_out_temp[j])
                            block_out_temp[j] = Activation(activation)(
                                block_out_temp[j])
                            block_out_temp[j] = Dropout(dropout_rate)(
                                block_out_temp[j])

                            block_out[j] = Add()(
                                [block_out[j], block_out_temp[j]])
        return block_out
    # ============================= CALCULATIONS ============================= #
    input_samples = int(input_time * fs / 1000)
    scales_samples = [int(s * fs / 1000) for s in scales_time]

    # ================================ INPUT ================================= #
    input_layer = Input((input_samples, ncha, 1))
    input = tf.expand_dims(input_layer, axis=1)
    if symmetric:
        superposition = False
        if ch_lateral &lt; ncha // 2:
            superposition = True
        ncha = ncha - ch_lateral

        left_idx = list(range(ch_lateral))
        ch_left = tf.gather(input, indices=left_idx, axis=-2)
        right_idx = list(np.array(left_idx) + int(ncha))
        ch_right = tf.gather(input, indices=right_idx, axis=-2)

        if superposition:
            # ch_central = crop(3, self.ch_lateral, -self.ch_lateral)(input)
            central_idx = list(
                np.array(range(ncha - ch_lateral)) + ch_lateral)
            ch_central = tf.gather(input, indices=central_idx, axis=-2)

            left_init = keras.layers.concatenate((ch_left, ch_central),
                                                 axis=-2)
            right_init = keras.layers.concatenate((ch_right, ch_central),
                                                  axis=-2)
        else:
            left_init = ch_left
            right_init = ch_right

        input = keras.layers.concatenate((left_init, right_init), axis=1)
        division = 2
    else:
        division = 1
    # ======================== TEMPOSPATIAL ANALYSIS ========================= #
    # ============================ Inception (x2) ============================ #
    b1_out = general_module([input],
                              scales_samples=scales_samples,
                              filters_per_branch=filters_per_branch,
                              ncha=ncha,
                              activation=activation,
                              dropout_rate=dropout_rate, average=2,
                              spatial_resnet_repetitions=spatial_resnet_repetitions,
                              residual=residual, init=True)

    b2_out = general_module(b1_out, scales_samples=[int(x / 4) for x in
                                                      scales_samples],
                              filters_per_branch=filters_per_branch,
                              ncha=ncha,
                              activation=activation,
                              dropout_rate=dropout_rate, average=2,
                              spatial_resnet_repetitions=spatial_resnet_repetitions,
                              residual=residual)
    # ============================== Residual (x3) =========================== #
    b3_u1 = general_module(b2_out, scales_samples=[16],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),
                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    b3_u1 = general_module(b3_u1,
                             scales_samples=[8],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),

                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    b3_u2 = general_module(b3_u1, scales_samples=[4],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 4),
                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    # ========================== TEMPORAL REDUCTION ========================== #
    t_red = b3_u2[0]
    for _ in range(1):
        t_red_temp = t_red
        t_red_temp = Conv3D(kernel_size=(1, 4, 1),
                            filters=int(filters_per_branch * len(
                                scales_samples) / 4),
                            use_bias=False,
                            strides=(1, 1, 1),
                            kernel_initializer=&#39;he_normal&#39;,
                            padding=&#39;same&#39;)(t_red_temp)
        t_red_temp = BatchNormalization()(t_red_temp)
        t_red_temp = Activation(activation)(t_red_temp)
        t_red_temp = Dropout(dropout_rate)(t_red_temp)

        if residual:
            t_red = Add()([t_red, t_red_temp])
        else:
            t_red = t_red_temp

    t_red = AveragePooling3D((1, 2, 1))(t_red)

    # =========================== CHANNEL MERGING ============================ #
    ch_merg = t_red
    if residual:
        for _ in range(2):
            ch_merg_temp = ch_merg
            ch_merg_temp = Conv3D(kernel_size=(division, 1, ncha),
                                  filters=int(filters_per_branch * len(
                                      scales_samples) / 4),
                                  use_bias=False,
                                  strides=(1, 1, 1),
                                  kernel_initializer=&#39;he_normal&#39;,
                                  padding=&#39;valid&#39;)(ch_merg_temp)
            ch_merg_temp = BatchNormalization()(ch_merg_temp)
            ch_merg_temp = Activation(activation)(ch_merg_temp)
            ch_merg_temp = Dropout(dropout_rate)(ch_merg_temp)

            ch_merg = Add()([ch_merg, ch_merg_temp])

        ch_merg = Conv3D(kernel_size=(division, 1, ncha),
                         filters=int(
                             filters_per_branch * len(scales_samples) / 4),
                         groups=int(
                             filters_per_branch * len(scales_samples) / 8),
                         use_bias=False,
                         padding=&#39;valid&#39;)(ch_merg)
        ch_merg = BatchNormalization()(ch_merg)
        ch_merg = Activation(activation)(ch_merg)
        ch_merg = Dropout(dropout_rate)(ch_merg)
    else:
        if symmetric:
            ch_merg = Conv3D(kernel_size=(division, 1, 1),
                             filters=int(
                                 filters_per_branch * len(
                                     scales_samples) / 4),
                             groups=int(
                                 filters_per_branch * len(
                                     scales_samples) / 8),
                             use_bias=False,
                             padding=&#39;valid&#39;)(ch_merg)
            ch_merg = BatchNormalization()(ch_merg)
            ch_merg = Activation(activation)(ch_merg)
            ch_merg = Dropout(dropout_rate)(ch_merg)
    # ========================== TEMPORAL MERGING ============================ #
    t_merg = ch_merg
    for _ in range(1):
        if residual:
            t_merg_temp = t_merg
            t_merg_temp = Conv3D(kernel_size=(1, input_samples // 64, 1),
                                 filters=int(filters_per_branch * len(
                                     scales_samples) / 4),
                                 use_bias=False,
                                 strides=(1, 1, 1),
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;valid&#39;)(t_merg_temp)
            t_merg_temp = BatchNormalization()(t_merg_temp)
            t_merg_temp = Activation(activation)(t_merg_temp)
            t_merg_temp = Dropout(dropout_rate)(t_merg_temp)

            t_merg = Add()([t_merg, t_merg_temp])
        else:
            t_merg_temp = t_merg
            t_merg_temp = Conv3D(kernel_size=(1, input_samples // 64, 1),
                                 filters=int(filters_per_branch * len(
                                     scales_samples) / 4),
                                 use_bias=False,
                                 strides=(1, 1, 1),
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;same&#39;)(t_merg_temp)
            t_merg_temp = BatchNormalization()(t_merg_temp)
            t_merg_temp = Activation(activation)(t_merg_temp)
            t_merg_temp = Dropout(dropout_rate)(t_merg_temp)
            t_merg = t_merg_temp

    t_merg = Conv3D(kernel_size=(1, input_samples // 64, 1),
                    filters=int(
                        filters_per_branch * len(scales_samples) / 4) * 2,
                    groups=int(
                        filters_per_branch * len(scales_samples) / 4),
                    use_bias=False,
                    padding=&#39;valid&#39;)(t_merg)
    t_merg = BatchNormalization()(t_merg)
    t_merg = Activation(activation)(t_merg)
    t_merg = Dropout(dropout_rate)(t_merg)
    # =============================== OUTPUT ================================= #
    output = t_merg
    for _ in range(4):
        output_temp = output
        output_temp = Conv3D(kernel_size=(1, 1, 1),
                             filters=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),
                             use_bias=False,
                             strides=(1, 1, 1),
                             kernel_initializer=&#39;he_normal&#39;,
                             padding=&#39;valid&#39;)(output_temp)
        output_temp = BatchNormalization()(output_temp)
        output_temp = Activation(activation)(output_temp)
        output_temp = Dropout(dropout_rate)(output_temp)
        if residual:
            output = Add()([output, output_temp])
        else:
            output = output_temp
    output = Flatten()(output)
    output_layer = Dense(n_classes, activation=&#39;softmax&#39;)(output)
    # Create and compile model
    model = keras.models.Model(inputs=input_layer, outputs=output_layer)
    optimizer = keras.optimizers.Adam(learning_rate=learning_rate,
                                      beta_1=0.9, beta_2=0.999,
                                      amsgrad=False)
    model.compile(loss=&#39;categorical_crossentropy&#39;, optimizer=optimizer,
                  metrics=[&#39;accuracy&#39;])
    return model</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-functions">Functions</h2>
<dl>
<dt id="EEGSym.EEGSym_architecture.EEGSym"><code class="name flex">
<span>def <span class="ident">EEGSym</span></span>(<span>input_time=3000, fs=128, ncha=8, filters_per_branch=8, scales_time=(500, 250, 125), dropout_rate=0.25, activation='elu', n_classes=2, learning_rate=0.001, ch_lateral=3, spatial_resnet_repetitions=1, residual=True, symmetric=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Keras implementation of EEGSym.</p>
<p>This model was initially designed for MI decodification of either
left/right hand.
Hyperparameters and architectural choices are explained in the
original article.</p>
<h2 id="parameters">Parameters</h2>
<dl>
<dt><strong><code>input_time</code></strong> :&ensp;<code>int</code></dt>
<dd>EEG epoch time in milliseconds.</dd>
<dt><strong><code>fs</code></strong> :&ensp;<code>int</code></dt>
<dd>Sample rate of the EEG.</dd>
<dt>ncha :</dt>
<dt>Number of input channels.</dt>
<dt><strong><code>filters_per_branch</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of filters in each Inception branch. The number should be
multiplies of 8.</dd>
<dt><strong><code>scales_time</code></strong> :&ensp;<code>list</code></dt>
<dd>Temporal scale of the temporal convolutions on first Inception module.
This parameter determines the kernel sizes of the filters.</dd>
<dt><strong><code>dropout_rate</code></strong> :&ensp;<code>float</code></dt>
<dd>Dropout rate</dd>
<dt><strong><code>activation</code></strong> :&ensp;<code>str</code></dt>
<dd>Activation</dd>
<dt><strong><code>n_classes</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of output classes</dd>
<dt><strong><code>learning_rate</code></strong> :&ensp;<code>float</code></dt>
<dd>Learning rate</dd>
<dt><strong><code>ch_lateral</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of channels that are attributed to one hemisphere of the head.</dd>
<dt><strong><code>spatial_resnet_repetitions</code></strong> :&ensp;<code>int</code></dt>
<dd>Number of repetitions of the operations of spatial analysis at each
step of the spatiotemporal analysis. In the original publication this
value was set to 1 and not tested its variations.</dd>
<dt><strong><code>residual</code></strong> :&ensp;<code>Bool</code></dt>
<dd>If the residual operations are present in EEGSym architecture.</dd>
<dt><strong><code>symmetric</code></strong> :&ensp;<code>Bool</code></dt>
<dd>If the architecture considers the parameter ch_lateral to create two
symmetric inputs of the electrodes.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><strong><code>model</code></strong> :&ensp;<code>keras.models.Model</code></dt>
<dd>Keras model already compiled and ready to work</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def EEGSym(input_time=3000, fs=128, ncha=8, filters_per_branch=8,
           scales_time=(500, 250, 125), dropout_rate=0.25, activation=&#39;elu&#39;,
           n_classes=2, learning_rate=0.001, ch_lateral=3,
           spatial_resnet_repetitions=1, residual=True, symmetric=True):

    &#34;&#34;&#34;Keras implementation of EEGSym.

    This model was initially designed for MI decodification of either
    left/right hand.
    Hyperparameters and architectural choices are explained in the
    original article.

    Parameters
    ----------
    input_time : int
        EEG epoch time in milliseconds.
    fs : int
        Sample rate of the EEG.
    ncha :
        Number of input channels.
    filters_per_branch : int
        Number of filters in each Inception branch. The number should be
        multiplies of 8.
    scales_time : list
        Temporal scale of the temporal convolutions on first Inception module.
        This parameter determines the kernel sizes of the filters.
    dropout_rate : float
        Dropout rate
    activation : str
        Activation
    n_classes : int
        Number of output classes
    learning_rate : float
        Learning rate
    ch_lateral : int
        Number of channels that are attributed to one hemisphere of the head.
    spatial_resnet_repetitions: int
        Number of repetitions of the operations of spatial analysis at each
        step of the spatiotemporal analysis. In the original publication this
        value was set to 1 and not tested its variations.
    residual : Bool
        If the residual operations are present in EEGSym architecture.
    symmetric : Bool
        If the architecture considers the parameter ch_lateral to create two
        symmetric inputs of the electrodes.

    Returns
    -------
    model : keras.models.Model
        Keras model already compiled and ready to work
    &#34;&#34;&#34;

    # ======================================================================== #
    # ================== GENERAL INCEPTION/RESIDUAL MODULE =================== #
    def general_module(input, scales_samples, filters_per_branch, ncha,
                         activation, dropout_rate, average,
                         spatial_resnet_repetitions=1, residual=True,
                         init=False):
        &#34;&#34;&#34;General inception/residual module.

            This function returns the input with the operations of a
            inception or residual module from the publication applied.

            Parameters
            ----------
            input : list
                List of input blocks to the module.
            scales_samples : list
                List of samples size of the temporal operations kernels.
            filters_per_branch : int
                Number of filters in each Inception branch. The number should be
                multiplies of 8.
            ncha :
                Number of input channels.
            activation : str
                Activation
            dropout_rate : float
                Dropout rate
            spatial_resnet_repetitions: int
                Number of repetitions of the operations of spatial analysis at
                each step of the spatiotemporal analysis. In the original
                publication this value was set to 1 and not tested its
                variations.
            residual : Bool
                If the residual operations are present in EEGSym architecture.
            init : Bool
                If the module is the first one applied to the input, to apply a
                channel merging operation if the architecture does not include
                residual operations.

            Returns
            -------
            block_out : list
                List of outputs modules
        &#34;&#34;&#34;
        block_units = list()
        unit_conv_t = list()
        unit_batchconv_t = list()

        for i in range(len(scales_samples)):
            unit_conv_t.append(Conv3D(filters=filters_per_branch,
                                      kernel_size=(1, scales_samples[i], 1),
                                      kernel_initializer=&#39;he_normal&#39;,
                                      padding=&#39;same&#39;))
            unit_batchconv_t.append(BatchNormalization())

        if ncha != 1:
            unit_dconv = list()
            unit_batchdconv = list()
            unit_conv_s = list()
            unit_batchconv_s = list()
            for i in range(spatial_resnet_repetitions):
                # 3D Implementation of DepthwiseConv
                unit_dconv.append(Conv3D(kernel_size=(1, 1, ncha),
                                         filters=filters_per_branch * len(
                                             scales_samples),
                                         groups=filters_per_branch * len(
                                             scales_samples),
                                         use_bias=False,
                                         padding=&#39;valid&#39;))
                unit_batchdconv.append(BatchNormalization())

                unit_conv_s.append(Conv3D(kernel_size=(1, 1, ncha),
                                          filters=filters_per_branch,
                                          # groups=filters_per_branch,
                                          use_bias=False,
                                          strides=(1, 1, 1),
                                          kernel_initializer=&#39;he_normal&#39;,
                                          padding=&#39;valid&#39;))
                unit_batchconv_s.append(BatchNormalization())

            unit_conv_1 = Conv3D(kernel_size=(1, 1, 1),
                                 filters=filters_per_branch,
                                 use_bias=False,
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;valid&#39;)
            unit_batchconv_1 = BatchNormalization()

        for j in range(len(input)):
            block_side_units = list()
            for i in range(len(scales_samples)):
                unit = input[j]
                unit = unit_conv_t[i](unit)

                unit = unit_batchconv_t[i](unit)
                unit = Activation(activation)(unit)
                unit = Dropout(dropout_rate)(unit)

                block_side_units.append(unit)
            block_units.append(block_side_units)
        # Concatenation
        block_out = list()
        for j in range(len(input)):
            if len(block_units[j]) != 1:
                block_out.append(
                    keras.layers.concatenate(block_units[j], axis=-1))
            else:
                block_out.append(block_units[j][0])

            if residual:
                if len(block_units[j]) != 1:
                    block_out_temp = input[j]
                else:
                    block_out_temp = input[j]
                    block_out_temp = unit_conv_1(block_out_temp)

                    block_out_temp = unit_batchconv_1(block_out_temp)
                    block_out_temp = Activation(activation)(block_out_temp)
                    block_out_temp = Dropout(dropout_rate)(block_out_temp)

                block_out[j] = Add()([block_out[j], block_out_temp])

            if average != 1:
                block_out[j] = AveragePooling3D((1, average, 1))(block_out[j])

        if ncha != 1:
            for i in range(spatial_resnet_repetitions):
                block_out_temp = list()
                for j in range(len(input)):
                    if len(scales_samples) != 1:
                        if residual:
                            block_out_temp.append(block_out[j])

                            block_out_temp[j] = unit_dconv[i](block_out_temp[j])

                            block_out_temp[j] = unit_batchdconv[i](
                                block_out_temp[j])
                            block_out_temp[j] = Activation(activation)(
                                block_out_temp[j])
                            block_out_temp[j] = Dropout(dropout_rate)(
                                block_out_temp[j])

                            block_out[j] = Add()(
                                [block_out[j], block_out_temp[j]])

                        elif init:
                            block_out[j] = unit_dconv[i](block_out[j])
                            block_out[j] = unit_batchdconv[i](block_out[j])
                            block_out[j] = Activation(activation)(block_out[j])
                            block_out[j] = Dropout(dropout_rate)(block_out[j])
                    else:
                        if residual:
                            block_out_temp.append(block_out[j])

                            block_out_temp[j] = unit_conv_s[i](
                                block_out_temp[j])
                            block_out_temp[j] = unit_batchconv_s[i](
                                block_out_temp[j])
                            block_out_temp[j] = Activation(activation)(
                                block_out_temp[j])
                            block_out_temp[j] = Dropout(dropout_rate)(
                                block_out_temp[j])

                            block_out[j] = Add()(
                                [block_out[j], block_out_temp[j]])
        return block_out
    # ============================= CALCULATIONS ============================= #
    input_samples = int(input_time * fs / 1000)
    scales_samples = [int(s * fs / 1000) for s in scales_time]

    # ================================ INPUT ================================= #
    input_layer = Input((input_samples, ncha, 1))
    input = tf.expand_dims(input_layer, axis=1)
    if symmetric:
        superposition = False
        if ch_lateral &lt; ncha // 2:
            superposition = True
        ncha = ncha - ch_lateral

        left_idx = list(range(ch_lateral))
        ch_left = tf.gather(input, indices=left_idx, axis=-2)
        right_idx = list(np.array(left_idx) + int(ncha))
        ch_right = tf.gather(input, indices=right_idx, axis=-2)

        if superposition:
            # ch_central = crop(3, self.ch_lateral, -self.ch_lateral)(input)
            central_idx = list(
                np.array(range(ncha - ch_lateral)) + ch_lateral)
            ch_central = tf.gather(input, indices=central_idx, axis=-2)

            left_init = keras.layers.concatenate((ch_left, ch_central),
                                                 axis=-2)
            right_init = keras.layers.concatenate((ch_right, ch_central),
                                                  axis=-2)
        else:
            left_init = ch_left
            right_init = ch_right

        input = keras.layers.concatenate((left_init, right_init), axis=1)
        division = 2
    else:
        division = 1
    # ======================== TEMPOSPATIAL ANALYSIS ========================= #
    # ============================ Inception (x2) ============================ #
    b1_out = general_module([input],
                              scales_samples=scales_samples,
                              filters_per_branch=filters_per_branch,
                              ncha=ncha,
                              activation=activation,
                              dropout_rate=dropout_rate, average=2,
                              spatial_resnet_repetitions=spatial_resnet_repetitions,
                              residual=residual, init=True)

    b2_out = general_module(b1_out, scales_samples=[int(x / 4) for x in
                                                      scales_samples],
                              filters_per_branch=filters_per_branch,
                              ncha=ncha,
                              activation=activation,
                              dropout_rate=dropout_rate, average=2,
                              spatial_resnet_repetitions=spatial_resnet_repetitions,
                              residual=residual)
    # ============================== Residual (x3) =========================== #
    b3_u1 = general_module(b2_out, scales_samples=[16],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),
                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    b3_u1 = general_module(b3_u1,
                             scales_samples=[8],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),

                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    b3_u2 = general_module(b3_u1, scales_samples=[4],
                             filters_per_branch=int(
                                 filters_per_branch * len(
                                     scales_samples) / 4),
                             ncha=ncha,
                             activation=activation,
                             dropout_rate=dropout_rate, average=2,
                             spatial_resnet_repetitions=spatial_resnet_repetitions,
                             residual=residual)
    # ========================== TEMPORAL REDUCTION ========================== #
    t_red = b3_u2[0]
    for _ in range(1):
        t_red_temp = t_red
        t_red_temp = Conv3D(kernel_size=(1, 4, 1),
                            filters=int(filters_per_branch * len(
                                scales_samples) / 4),
                            use_bias=False,
                            strides=(1, 1, 1),
                            kernel_initializer=&#39;he_normal&#39;,
                            padding=&#39;same&#39;)(t_red_temp)
        t_red_temp = BatchNormalization()(t_red_temp)
        t_red_temp = Activation(activation)(t_red_temp)
        t_red_temp = Dropout(dropout_rate)(t_red_temp)

        if residual:
            t_red = Add()([t_red, t_red_temp])
        else:
            t_red = t_red_temp

    t_red = AveragePooling3D((1, 2, 1))(t_red)

    # =========================== CHANNEL MERGING ============================ #
    ch_merg = t_red
    if residual:
        for _ in range(2):
            ch_merg_temp = ch_merg
            ch_merg_temp = Conv3D(kernel_size=(division, 1, ncha),
                                  filters=int(filters_per_branch * len(
                                      scales_samples) / 4),
                                  use_bias=False,
                                  strides=(1, 1, 1),
                                  kernel_initializer=&#39;he_normal&#39;,
                                  padding=&#39;valid&#39;)(ch_merg_temp)
            ch_merg_temp = BatchNormalization()(ch_merg_temp)
            ch_merg_temp = Activation(activation)(ch_merg_temp)
            ch_merg_temp = Dropout(dropout_rate)(ch_merg_temp)

            ch_merg = Add()([ch_merg, ch_merg_temp])

        ch_merg = Conv3D(kernel_size=(division, 1, ncha),
                         filters=int(
                             filters_per_branch * len(scales_samples) / 4),
                         groups=int(
                             filters_per_branch * len(scales_samples) / 8),
                         use_bias=False,
                         padding=&#39;valid&#39;)(ch_merg)
        ch_merg = BatchNormalization()(ch_merg)
        ch_merg = Activation(activation)(ch_merg)
        ch_merg = Dropout(dropout_rate)(ch_merg)
    else:
        if symmetric:
            ch_merg = Conv3D(kernel_size=(division, 1, 1),
                             filters=int(
                                 filters_per_branch * len(
                                     scales_samples) / 4),
                             groups=int(
                                 filters_per_branch * len(
                                     scales_samples) / 8),
                             use_bias=False,
                             padding=&#39;valid&#39;)(ch_merg)
            ch_merg = BatchNormalization()(ch_merg)
            ch_merg = Activation(activation)(ch_merg)
            ch_merg = Dropout(dropout_rate)(ch_merg)
    # ========================== TEMPORAL MERGING ============================ #
    t_merg = ch_merg
    for _ in range(1):
        if residual:
            t_merg_temp = t_merg
            t_merg_temp = Conv3D(kernel_size=(1, input_samples // 64, 1),
                                 filters=int(filters_per_branch * len(
                                     scales_samples) / 4),
                                 use_bias=False,
                                 strides=(1, 1, 1),
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;valid&#39;)(t_merg_temp)
            t_merg_temp = BatchNormalization()(t_merg_temp)
            t_merg_temp = Activation(activation)(t_merg_temp)
            t_merg_temp = Dropout(dropout_rate)(t_merg_temp)

            t_merg = Add()([t_merg, t_merg_temp])
        else:
            t_merg_temp = t_merg
            t_merg_temp = Conv3D(kernel_size=(1, input_samples // 64, 1),
                                 filters=int(filters_per_branch * len(
                                     scales_samples) / 4),
                                 use_bias=False,
                                 strides=(1, 1, 1),
                                 kernel_initializer=&#39;he_normal&#39;,
                                 padding=&#39;same&#39;)(t_merg_temp)
            t_merg_temp = BatchNormalization()(t_merg_temp)
            t_merg_temp = Activation(activation)(t_merg_temp)
            t_merg_temp = Dropout(dropout_rate)(t_merg_temp)
            t_merg = t_merg_temp

    t_merg = Conv3D(kernel_size=(1, input_samples // 64, 1),
                    filters=int(
                        filters_per_branch * len(scales_samples) / 4) * 2,
                    groups=int(
                        filters_per_branch * len(scales_samples) / 4),
                    use_bias=False,
                    padding=&#39;valid&#39;)(t_merg)
    t_merg = BatchNormalization()(t_merg)
    t_merg = Activation(activation)(t_merg)
    t_merg = Dropout(dropout_rate)(t_merg)
    # =============================== OUTPUT ================================= #
    output = t_merg
    for _ in range(4):
        output_temp = output
        output_temp = Conv3D(kernel_size=(1, 1, 1),
                             filters=int(
                                 filters_per_branch * len(
                                     scales_samples) / 2),
                             use_bias=False,
                             strides=(1, 1, 1),
                             kernel_initializer=&#39;he_normal&#39;,
                             padding=&#39;valid&#39;)(output_temp)
        output_temp = BatchNormalization()(output_temp)
        output_temp = Activation(activation)(output_temp)
        output_temp = Dropout(dropout_rate)(output_temp)
        if residual:
            output = Add()([output, output_temp])
        else:
            output = output_temp
    output = Flatten()(output)
    output_layer = Dense(n_classes, activation=&#39;softmax&#39;)(output)
    # Create and compile model
    model = keras.models.Model(inputs=input_layer, outputs=output_layer)
    optimizer = keras.optimizers.Adam(learning_rate=learning_rate,
                                      beta_1=0.9, beta_2=0.999,
                                      amsgrad=False)
    model.compile(loss=&#39;categorical_crossentropy&#39;, optimizer=optimizer,
                  metrics=[&#39;accuracy&#39;])
    return model</code></pre>
</details>
</dd>
</dl>
</section>
<section>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="EEGSym" href="index.html">EEGSym</a></code></li>
</ul>
</li>
<li><h3><a href="#header-functions">Functions</a></h3>
<ul class="">
<li><code><a title="EEGSym.EEGSym_architecture.EEGSym" href="#EEGSym.EEGSym_architecture.EEGSym">EEGSym</a></code></li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>